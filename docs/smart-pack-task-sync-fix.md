# 智能打包任务状态同步修复

## 问题描述

在实施智能打包路径修复后，出现了新的临时文件管理和任务状态同步问题：

1. **临时文件清理问题**：应用关闭时出现 "ENOENT: no such file or directory" 错误
2. **任务重复问题**：重新打开应用后出现两个相同的智能打包任务
3. **事件监听器泄漏**：智能打包过程中的事件监听器没有正确清理
4. **任务状态不一致**：智能打包任务的状态在应用重启后可能不一致

## 根本原因分析

### 1. 事件监听器生命周期管理不当
- 在 `smartPackUpload` 方法中使用 `on` 监听器，可能导致重复触发
- 应用异常关闭时监听器没有被正确清理
- 重启后可能重复触发压缩完成事件

### 2. 任务去重机制缺失
- 应用重启后没有对智能打包任务进行去重检查
- 可能导致相同的智能打包任务被重复创建

### 3. 临时文件清理错误处理不完善
- 文件不存在时的错误处理不够健壮
- 没有在清理前检查文件是否存在

### 4. 任务状态验证不足
- 恢复任务时没有验证智能打包文件是否存在
- 缺少对任务数据完整性的检查

## 修复方案

### 1. 改进事件监听器管理

**文件**：`electron/tus/uploadManager.ts`

```typescript
// 🔧 修复：使用 Promise 和 once 监听器来避免重复触发和内存泄漏
const compressionPromise = new Promise<{ success: boolean; taskId?: string; error?: string }>((resolve) => {
  const handleArchiveCompleted = async (taskId: string, result: any) => {
    if (taskId !== archiveTaskId) return;
    // ... 处理逻辑
  };

  const handleArchiveError = (taskId: string, error: string) => {
    if (taskId !== archiveTaskId) return;
    // ... 错误处理
  };

  // 🔧 修复：使用 once 而不是 on 来避免重复触发
  this.archiveManager.once("task-completed", handleArchiveCompleted);
  this.archiveManager.once("task-error", handleArchiveError);
});
```

**改进点**：
- 使用 `once` 替代 `on` 避免重复触发
- 使用 Promise 包装异步操作，确保正确的生命周期管理
- 添加任务ID检查，避免处理错误的事件

### 2. 添加任务去重机制

**文件**：`electron/tus/uploadManager.ts`

```typescript
/**
 * 查找现有的智能打包任务
 */
private findExistingSmartPackTask(filePaths: string[], archiveName: string): UploadTask | undefined {
  const filePathsSet = new Set(filePaths);

  for (const task of this.tasks.values()) {
    if (task.metadata?.uploadType === "smart-packed-archive" && 
        task.metadata?.archiveName === archiveName &&
        ["pending", "uploading", "paused"].includes(task.status)) {
      
      // 检查原始文件列表是否匹配
      try {
        const taskOriginalFiles = JSON.parse(task.metadata.originalFiles || "[]") as string[];
        const taskFilePathsSet = new Set(taskOriginalFiles);
        
        // 如果文件列表完全匹配，认为是重复任务
        if (taskFilePathsSet.size === filePathsSet.size && 
            [...taskFilePathsSet].every((path: string) => filePathsSet.has(path))) {
          return task;
        }
      } catch (error) {
        tusLogger.warn(`解析任务原始文件列表失败: ${task.fileName}`, error);
      }
    }
  }
  
  return undefined;
}
```

**改进点**：
- 基于文件路径和压缩包名称进行去重检查
- 在创建新任务前检查是否已存在相同任务
- 返回现有任务而不是创建重复任务

### 3. 改进临时文件清理

**文件**：`electron/tus/uploadManager.ts`

```typescript
/**
 * 清理临时文件
 */
private async cleanupTempFile(task: UploadTask): Promise<void> {
  // 🔧 修复：智能打包的压缩文件不立即清理，保留用于断点续传
  if (task.metadata?.uploadType === "smart-packed-archive") {
    tusLogger.info(`智能打包文件保留用于断点续传: ${task.filePath}`);
    return;
  }

  if (task.metadata?.tempFilePath) {
    try {
      const fs = await import("fs/promises");
      // 🔧 修复：先检查文件是否存在，避免 ENOENT 错误
      try {
        await fs.access(task.metadata.tempFilePath);
        await fs.unlink(task.metadata.tempFilePath);
        tusLogger.info(`已清理临时文件: ${task.metadata.tempFilePath}`);
      } catch (accessError: any) {
        if (accessError.code === "ENOENT") {
          tusLogger.debug(`临时文件已不存在，跳过清理: ${task.metadata.tempFilePath}`);
        } else {
          throw accessError;
        }
      }
    } catch (error) {
      tusLogger.warn(`清理临时文件失败: ${task.metadata.tempFilePath}`, error);
    }
  }
}
```

**改进点**：
- 在删除文件前先检查文件是否存在
- 对 ENOENT 错误进行特殊处理，避免抛出异常
- 保留智能打包文件用于断点续传

### 4. 改进任务恢复逻辑

**文件**：`electron/tus/uploadManager.ts`

```typescript
private restoreUnfinishedTasks(): void {
  const storedTasks = this.store.get("tasks", {}) as Record<string, UploadTask>;
  const smartPackTasks: UploadTask[] = [];
  const regularTasks: UploadTask[] = [];

  // 🔧 修复：分离智能打包任务和普通任务
  Object.values(storedTasks).forEach((storedTask) => {
    if (["uploading", "paused", "pending", "completed"].includes(storedTask.status)) {
      if (storedTask.metadata?.uploadType === "smart-packed-archive") {
        smartPackTasks.push(storedTask);
      } else {
        regularTasks.push(storedTask);
      }
    }
  });

  // 恢复普通任务
  regularTasks.forEach((storedTask) => {
    // 将之前正在上传的任务标记为暂停
    if (storedTask.status === "uploading") {
      storedTask.status = "paused";
    }

    this.tasks.set(storedTask.id, {
      ...storedTask,
      startTime: new Date(storedTask.startTime),
    });
  });

  // 🔧 修复：对智能打包任务进行去重和状态验证
  this.restoreSmartPackTasks(smartPackTasks);
}
```

**改进点**：
- 分离智能打包任务和普通任务的恢复逻辑
- 对智能打包任务进行专门的去重和状态验证
- 异步处理智能打包任务恢复，避免阻塞

### 5. 修复文件流创建错误

**文件**：`electron/tus/uploadManager.ts`

```typescript
} else {
  // 普通文件，创建文件流
  const fs = await import("fs");

  // 🔧 修复：在创建文件流前检查文件是否存在
  try {
    await fs.promises.access(task.filePath);
    file = fs.createReadStream(task.filePath);
  } catch (error: any) {
    if (error.code === "ENOENT") {
      throw new Error(`文件不存在: ${task.filePath}`);
    }
    throw error;
  }
}
```

**改进点**：
- 在创建文件流前检查文件是否存在
- 提供更友好的错误信息
- 避免 ENOENT 异常导致应用崩溃

### 6. 修复任务恢复同步问题

**文件**：`electron/tus/uploadManager.ts`

```typescript
/**
 * 同步恢复智能打包任务，包含去重和状态验证
 */
private restoreSmartPackTasksSync(smartPackTasks: UploadTask[]): void {
  const taskMap = new Map<string, UploadTask>();

  // 基于 archiveName 和 originalFiles 进行去重
  for (const task of smartPackTasks) {
    const archiveName = task.metadata?.archiveName;
    const originalFiles = task.metadata?.originalFiles;

    if (!archiveName || !originalFiles) {
      this.restoreSingleSmartPackTaskSync(task);
      continue;
    }

    const key = `${archiveName}_${originalFiles}`;
    const existingTask = taskMap.get(key);

    if (!existingTask) {
      taskMap.set(key, task);
    } else {
      // 保留最新的任务，清理重复任务
      const existingTime = new Date(existingTask.startTime).getTime();
      const currentTime = new Date(task.startTime).getTime();

      if (currentTime > existingTime) {
        taskMap.set(key, task);
        this.removeTaskFromStore(existingTask.id);
      } else {
        this.removeTaskFromStore(task.id);
      }
    }
  }

  // 恢复去重后的任务
  for (const task of taskMap.values()) {
    this.restoreSingleSmartPackTaskSync(task);
  }
}
```

**改进点**：
- 改为同步处理，避免任务恢复过程中的异步问题
- 先恢复任务到内存，文件验证延迟到实际使用时
- 确保任务不会在恢复过程中消失

## 最新修复总结

### 🔧 已解决的关键问题

1. **ENOENT 错误**：
   - 在创建文件流前检查文件是否存在
   - 提供友好的错误信息而不是崩溃

2. **任务消失问题**：
   - 改为同步任务恢复，避免异步处理导致的任务丢失
   - 先恢复任务到内存，延迟文件验证

3. **事件监听器泄漏**：
   - 使用 `once` 替代 `on` 避免重复触发
   - 使用 Promise 包装确保正确的生命周期管理

4. **任务重复**：
   - 基于文件路径和压缩包名称进行去重
   - 保留最新的任务，清理重复任务

## 预期效果

实施这些修复后，智能打包功能应该具备以下特性：

1. **稳定性**：不会因为文件不存在而崩溃
2. **一致性**：任务恢复后不会消失或重复
3. **健壮性**：正确处理各种异常情况
4. **用户友好**：提供清晰的错误信息和状态反馈

## 测试建议

1. **正常流程测试**：
   - 创建智能打包任务
   - 在上传过程中关闭应用
   - 重新打开应用，验证任务状态正确且不重复

2. **异常情况测试**：
   - 手动删除智能打包临时文件
   - 重启应用，验证任务仍然存在但标记为错误状态

3. **重复任务测试**：
   - 创建相同的智能打包任务
   - 验证去重机制是否生效

4. **文件流错误测试**：
   - 尝试恢复已删除文件的任务
   - 验证错误处理是否友好
