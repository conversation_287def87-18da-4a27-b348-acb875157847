# 智能打包临时文件管理修复文档

## 概述

本文档记录了智能打包上传临时文件管理问题的完整修复过程。该修复确保了智能打包的临时压缩文件在上传完成前不会被删除，使智能打包上传支持与普通文件上传相同的断点续传功能。

## 问题描述

### 原始问题
1. **临时文件存储位置**：智能打包的压缩文件存储在系统临时目录，容易被系统清理
2. **文件删除时机**：压缩文件在上传完成后立即被清理，但断点续传时仍需要这些文件
3. **缺少检查机制**：没有文件存在性检查API，无法验证临时文件是否存在
4. **无法重新生成**：智能打包任务没有保存原始文件信息，无法重新生成压缩文件

### 错误现象
```
Error: ENOENT: no such file or directory, stat '/tmp/clouddrive-archives/archive_xxx.7z'
```

## 修复方案

### 1. 实现文件存在性检查API

**文件**：`electron/tus/ipcHandlers.ts`

```typescript
// 检查文件是否存在
ipcMain.handle("tus-check-file-exists", async (_event, filePath: string): Promise<ApiResponse> => {
  try {
    const fs = await import("fs/promises");
    await fs.access(filePath);
    return { success: true, data: { exists: true } };
  } catch (error) {
    return { success: true, data: { exists: false } };
  }
});
```

**文件**：`electron/tus/preloadApi.ts`

```typescript
// 检查文件是否存在
checkFileExists: (filePath: string) => Promise<ApiResponse>;
```

### 2. 修改临时文件存储位置

**文件**：`electron/main.ts`

```typescript
// 🔧 修复：使用应用数据目录而非系统临时目录，确保文件持久化
const persistentArchiveDir = path.join(app.getPath("userData"), "temp-archives");

// 确保目录存在
if (!fs.existsSync(persistentArchiveDir)) {
  fs.mkdirSync(persistentArchiveDir, { recursive: true });
  mainLogger.info(`📦 创建持久化压缩文件目录: ${persistentArchiveDir}`);
}

const archiveConfig: Partial<ArchiveConfig> = {
  compressionLevel: 0, // 仅存储，不压缩
  format: "7z",
  tempDir: persistentArchiveDir, // 使用持久化目录
  maxConcurrent: 2,
};
```

### 3. 保存原始文件信息

**文件**：`electron/tus/uploadManager.ts`

```typescript
// 压缩成功，创建上传任务
const uploadTaskId = await this.createUploadTask(result.archivePath, {
  ...options?.metadata,
  originalName: `${archiveName}.7z`,
  uploadType: "smart-packed-archive",
  originalFileCount: filePaths.length.toString(),
  compressionStats: JSON.stringify(result.stats),
  // 🔧 修复：保存原始文件列表和根路径，用于重新生成压缩文件
  originalFiles: JSON.stringify(filePaths),
  rootPath: options?.rootPath || "",
  archiveName: archiveName,
});
```

### 4. 实现文件检查和重新生成机制

**文件**：`src/components/Upload/composables/useTusUpload.ts`

```typescript
/**
 * 检查智能打包任务的临时文件是否存在
 */
const checkSmartPackedTaskFile = async (task: UploadTask): Promise<boolean> => {
  try {
    if (isElectron.value && task.filePath) {
      const api = getElectronAPI();
      if (api && api.tus && typeof api.tus.checkFileExists === "function") {
        const response = await api.tus.checkFileExists(task.filePath);
        return response.success && response.data?.exists;
      }
    }
    return true;
  } catch (error) {
    tusLogger.warn(`检查智能打包文件失败: ${task.fileName}`, error);
    return false;
  }
};

/**
 * 尝试重新生成智能打包的压缩文件
 */
const attemptRegenerateArchive = async (task: UploadTask): Promise<boolean> => {
  try {
    if (!isElectron.value || !task.metadata) {
      return false;
    }

    // 检查是否有原始文件信息
    const originalFilesStr = task.metadata.originalFiles;
    const archiveName = task.metadata.archiveName;
    const rootPath = task.metadata.rootPath;

    if (!originalFilesStr || !archiveName) {
      return false;
    }

    const originalFiles = JSON.parse(originalFilesStr);

    // 调用智能打包上传重新生成压缩文件
    const api = getElectronAPI();
    if (api && api.tus && typeof api.tus.smartPackUpload === "function") {
      const result = await api.tus.smartPackUpload(originalFiles, {
        archiveName: archiveName,
        metadata: task.metadata,
        rootPath: rootPath || undefined,
        skipConfirmation: true,
      });

      return result.success;
    }

    return false;
  } catch (error) {
    tusLogger.error(`重新生成智能打包文件异常: ${task.fileName}`, error);
    return false;
  }
};
```

### 5. 修改临时文件清理策略

**文件**：`electron/tus/uploadManager.ts`

```typescript
/**
 * 清理临时文件
 */
private async cleanupTempFile(task: UploadTask): Promise<void> {
  // 🔧 修复：智能打包的压缩文件不立即清理，保留用于断点续传
  if (task.metadata?.uploadType === "smart-packed-archive") {
    tusLogger.info(`智能打包文件保留用于断点续传: ${task.filePath}`);
    return;
  }

  if (task.metadata?.tempFilePath) {
    try {
      const fs = await import("fs/promises");
      await fs.unlink(task.metadata.tempFilePath);
      tusLogger.info(`已清理临时文件: ${task.metadata.tempFilePath}`);
    } catch (error) {
      tusLogger.warn(`清理临时文件失败: ${task.metadata.tempFilePath}`, error);
    }
  }
}
```

### 6. 实现定期清理机制

**文件**：`electron/tus/uploadManager.ts`

```typescript
/**
 * 启动智能打包文件清理定时器
 */
private startSmartPackCleanupTimer(): void {
  // 每小时检查一次过期的智能打包文件
  setInterval(() => {
    this.cleanupExpiredSmartPackFiles().catch((error) => {
      tusLogger.error("清理过期智能打包文件失败:", error);
    });
  }, 60 * 60 * 1000); // 1小时

  // 启动时立即执行一次清理
  this.cleanupExpiredSmartPackFiles().catch((error) => {
    tusLogger.error("初始清理过期智能打包文件失败:", error);
  });
}

/**
 * 清理过期的智能打包文件
 */
private async cleanupExpiredSmartPackFiles(): Promise<void> {
  // 获取所有活跃的智能打包文件路径
  const allTasks = Array.from(this.tasks.values());
  const smartPackTasks = allTasks.filter((task) => task.metadata?.uploadType === "smart-packed-archive");
  const activeFilePaths = new Set(
    smartPackTasks
      .filter((task) => ["pending", "uploading", "paused"].includes(task.status))
      .map((task) => task.filePath)
      .filter(Boolean)
  );

  // 清理超过24小时且不被活跃任务使用的文件
  const { app } = await import("electron");
  const archiveDir = require("path").join(app.getPath("userData"), "temp-archives");
  
  const files = await fs.readdir(archiveDir);
  const now = Date.now();
  const maxAge = 24 * 60 * 60 * 1000; // 24小时

  for (const file of files) {
    if (!file.endsWith(".7z")) continue;

    const filePath = path.join(archiveDir, file);
    if (activeFilePaths.has(filePath)) continue;

    const stats = await fs.stat(filePath);
    const age = now - stats.mtime.getTime();

    if (age > maxAge) {
      await fs.unlink(filePath);
      tusLogger.info(`已清理过期智能打包文件: ${filePath}`);
    }
  }
}
```

## 技术特点

### 1. 持久化存储
- **应用数据目录**：使用`app.getPath("userData")/temp-archives`而非系统临时目录
- **目录自动创建**：启动时自动创建并确保目录存在
- **跨会话保持**：文件在应用重启后仍然存在

### 2. 智能清理机制
- **延迟清理**：上传完成后不立即删除，保留用于断点续传
- **定期清理**：每小时检查一次过期文件（超过24小时）
- **活跃任务保护**：正在使用的文件不会被清理

### 3. 自动恢复机制
- **文件存在性检查**：恢复任务时检查临时文件是否存在
- **自动重新生成**：文件丢失时尝试重新生成压缩文件
- **原始信息保存**：保存原始文件列表用于重新生成

### 4. 错误处理
- **优雅降级**：重新生成失败时标记为错误状态
- **用户友好提示**：提供清晰的错误信息
- **日志记录**：详细记录所有操作和错误

## 验证方法

### 1. 基本断点续传测试
```bash
# 测试步骤
1. 选择大量文件触发智能打包上传
2. 上传进行到一半时关闭应用
3. 重新打开应用
4. 检查任务是否能正常断点续传

# 预期结果
- 智能打包任务正常恢复
- 临时压缩文件存在且可访问
- 断点续传成功完成
```

### 2. 文件重新生成测试
```bash
# 测试步骤
1. 开始智能打包上传
2. 手动删除临时压缩文件
3. 重新打开应用
4. 检查是否自动重新生成

# 预期结果
- 检测到文件丢失
- 自动重新生成压缩文件
- 任务继续正常执行
```

### 3. 清理机制测试
```bash
# 测试步骤
1. 创建一些智能打包任务并完成
2. 等待24小时或手动触发清理
3. 检查过期文件是否被清理

# 预期结果
- 活跃任务的文件被保留
- 过期文件被正确清理
- 磁盘空间得到释放
```

## 相关文件

### 主要修改文件
- `electron/main.ts` - 持久化目录配置
- `electron/tus/ipcHandlers.ts` - 文件存在性检查API
- `electron/tus/preloadApi.ts` - API接口定义
- `electron/tus/uploadManager.ts` - 临时文件管理和清理机制
- `src/components/Upload/composables/useTusUpload.ts` - 文件检查和重新生成逻辑

### 相关文档
- `docs/smart-packing-temp-file-handling.md` - 原始问题分析文档

## 版本信息

- **修复版本**：v1.3.0
- **修复日期**：2024-12-19
- **影响范围**：智能打包上传功能
- **兼容性**：向后兼容，不影响现有功能

## 总结

本次修复通过以下关键改进解决了智能打包临时文件管理问题：

1. **持久化存储**：将临时文件存储在应用数据目录，确保跨会话保持
2. **延迟清理**：上传完成后不立即删除文件，保留用于断点续传
3. **自动恢复**：实现文件检查和自动重新生成机制
4. **智能清理**：定期清理过期文件，避免磁盘空间浪费

这些改进确保了智能打包上传具有与普通文件上传相同的断点续传稳定性，显著提升了用户体验。
