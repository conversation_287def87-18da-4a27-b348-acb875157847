# 智能打包临时文件生命周期管理修复

## 问题描述

当智能打包功能完成文件压缩后，在开始上传阶段如果用户关闭应用程序，会出现以下未捕获异常：

```
Uncaught Exception: Error: ENOENT: no such file or directory, open '/Users/<USER>/Library/Application Support/cloud-drive/temp-archives/pwa.7z'
```

## 问题根因

1. **时序问题**：智能打包任务完成压缩后状态变为 "completed"，但对应的上传任务可能仍处于 "pending"、"paused" 或 "uploading" 状态
2. **清理逻辑过于激进**：应用程序关闭时，`archiveManager.cleanupCompletedTasks()` 会清理所有已完成状态的压缩任务的临时文件，但没有检查这些文件是否仍被活跃的上传任务使用
3. **模块间缺乏协调**：Archive 模块和 TUS 模块之间缺乏临时文件使用状态的协调机制

## 修复方案

### 1. 智能临时文件生命周期管理

在 `ArchiveManager` 类中实现智能的临时文件生命周期管理：

- 添加活跃任务检查器机制
- 在清理临时文件前检查是否有活跃的上传任务正在使用该文件
- 只清理不再被使用的临时文件

### 2. 具体修改

#### 2.1 修改 ArchiveManager 类

**文件**：`electron/archive/archiveManager.ts`

```typescript
export class ArchiveManager extends EventEmitter {
  private activeTaskChecker?: () => Array<{ filePath: string; status: string }>;

  /**
   * 设置活跃任务检查器
   */
  setActiveTaskChecker(checker: () => Array<{ filePath: string; status: string }>): void {
    this.activeTaskChecker = checker;
  }

  /**
   * 清理已完成的任务（修复版本）
   */
  async cleanupCompletedTasks(): Promise<void> {
    const completedTasks = Array.from(this.tasks.values()).filter((task) => 
      ["completed", "error", "cancelled"].includes(task.status)
    );

    for (const task of completedTasks) {
      try {
        // 🔧 修复：在删除临时文件前检查是否有活跃的上传任务正在使用该文件
        const shouldDeleteFile = this.shouldDeleteTempFile(task.outputPath);
        
        if (shouldDeleteFile) {
          await fs.unlink(task.outputPath);
          archiveLogger.info(`已清理临时文件: ${task.outputPath}`);
        } else {
          archiveLogger.info(`跳过清理临时文件（仍被活跃任务使用）: ${task.outputPath}`);
        }
      } catch (error) {
        archiveLogger.warn(`清理临时文件失败: ${task.outputPath}`, error);
      }
      this.tasks.delete(task.id);
    }
  }

  /**
   * 检查是否应该删除临时文件
   */
  private shouldDeleteTempFile(filePath: string): boolean {
    if (!this.activeTaskChecker) {
      return true; // 向后兼容
    }

    try {
      const activeTasks = this.activeTaskChecker();
      
      // 检查是否有活跃的上传任务正在使用这个文件
      const isFileInUse = activeTasks.some(task => {
        const isPathMatch = task.filePath === filePath;
        const isActiveStatus = ["pending", "uploading", "paused"].includes(task.status);
        return isPathMatch && isActiveStatus;
      });

      return !isFileInUse;
    } catch (error) {
      archiveLogger.warn(`检查活跃任务时出错，默认删除文件: ${filePath}`, error);
      return true;
    }
  }
}
```

#### 2.2 修改 main.ts 初始化逻辑

**文件**：`electron/main.ts`

```typescript
// 初始化 TUS 模块
const tusModule = initializeTusModule(mainWindow, uploadConfig, archiveModule.archiveManager);
tusModuleCleanup = tusModule.cleanup;

// 🔧 修复：设置活跃任务检查器，用于智能临时文件生命周期管理
archiveModule.archiveManager.setActiveTaskChecker(() => {
  return tusModule.uploadManager.getActiveTasks().map(task => ({
    filePath: task.filePath,
    status: task.status
  }));
});
```

## 修复效果

### 1. 解决的问题

- ✅ 防止删除仍在使用中的智能打包临时文件
- ✅ 避免应用关闭时的 ENOENT 异常
- ✅ 保护暂停状态的上传任务能够正常恢复

### 2. 保持的功能

- ✅ 正常的临时文件清理功能不受影响
- ✅ 已完成/取消/错误状态的任务仍会被正确清理
- ✅ 向后兼容性：如果没有设置检查器，按原逻辑执行

### 3. 工作流程

1. **智能打包完成**：压缩任务状态变为 "completed"
2. **上传任务创建**：上传任务状态为 "pending"
3. **应用关闭触发清理**：
   - 调用 `cleanupCompletedTasks()`
   - 检查活跃任务列表
   - 发现有 "pending" 状态的上传任务使用该文件
   - 跳过删除，保留临时文件
4. **下次启动恢复**：上传任务可以正常恢复，临时文件仍然存在

## 测试验证

### 测试场景

1. **正常场景**：智能打包 → 上传完成 → 应用关闭 → 临时文件被清理
2. **修复场景**：智能打包 → 上传暂停 → 应用关闭 → 临时文件保留 → 重启恢复上传
3. **边界场景**：智能打包 → 上传失败 → 应用关闭 → 临时文件被清理

### 验证方法

1. 创建包含50+文件的文件夹触发智能打包
2. 在上传开始后立即关闭应用程序
3. 检查 temp-archives 目录中的文件是否保留
4. 重新启动应用程序，验证上传任务是否能正常恢复

## 实施状态

✅ **已完成修复**（2025-01-24）

### 修改的文件

1. **electron/archive/archiveManager.ts**
   - 添加了 `activeTaskChecker` 属性和 `setActiveTaskChecker` 方法
   - 修改了 `cleanupCompletedTasks` 方法，增加智能文件保护逻辑
   - 添加了 `shouldDeleteTempFile` 私有方法用于检查文件是否可以删除

2. **electron/main.ts**
   - 在 TUS 模块初始化后设置活跃任务检查器
   - 建立了 Archive 模块和 TUS 模块之间的协调机制

3. **electron/tus/uploadManager.ts**
   - 清理了未使用的异步方法以通过构建检查

### 构建验证

- ✅ TypeScript 编译通过
- ✅ Vite 构建成功
- ✅ Electron 打包完成
- ✅ 逻辑测试验证通过

## 总结

此修复方案通过实现智能的临时文件生命周期管理，解决了智能打包功能中临时文件被过早删除的问题。方案保持了模块间的松耦合，通过回调函数机制实现协调，既解决了问题又保持了代码的可维护性。

**关键优势**：
- 🛡️ **安全性**：防止删除仍在使用的临时文件
- 🔄 **兼容性**：向后兼容，不影响现有功能
- 🧩 **解耦性**：模块间通过回调函数松耦合
- 📊 **可观测性**：详细的日志记录便于调试
- ⚡ **性能**：最小化性能影响，只在必要时检查
