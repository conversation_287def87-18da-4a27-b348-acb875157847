<template>
  <div class="overflow-hidden min-h-screen bg-background">
    <!-- 认证检查加载状态 -->
    <div v-if="shouldShowLoading" class="flex justify-center items-center min-h-screen bg-background">
      <div class="flex flex-col gap-4 items-center">
        <div class="w-12 h-12 rounded-full border-4 animate-spin border-primary border-r-transparent"></div>
        <div class="text-center">
          <p class="text-muted-foreground">正在检查登录状态...</p>
        </div>
      </div>
    </div>

    <!-- 登录页面 -->
    <div v-else-if="shouldShowLogin">
      <router-view />
    </div>

    <!-- 主应用界面 -->
    <div v-else-if="shouldShowApp">
      <TooltipProvider>
        <SidebarProvider style="--sidebar-width: 9rem; --sidebar-width-mobile: 9rem;">
          <Sidebar />
          <main class="flex overflow-hidden flex-col w-full min-h-screen">
            <SidebarTrigger />
            <Navigation />
            <router-view class="flex-1" />
          </main>
        </SidebarProvider>

        <!-- 全局进度指示器 -->
        <GlobalProgressIndicator />

        <!-- 自动更新管理器（仅在Electron环境下） -->
        <AutoUpdaterManager v-if="isElectron" ref="autoUpdaterRef" :auto-check-on-mount="false" />
      </TooltipProvider>
    </div>

    <!-- 全局通知 -->
    <Toaster position="top-center" richColors />
  </div>
</template>

<script setup lang="ts">
import { ref, provide, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { Toaster } from '@/components/ui/sonner'
import 'vue-sonner/style.css'
import { SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar'
import { TooltipProvider } from '@/components/ui/tooltip'
import Sidebar from "@/components/Sidebar.vue";
import Navigation from "@/components/Navigation.vue";
import GlobalProgressIndicator from "@/components/GlobalProgressIndicator/index.vue";
import { AutoUpdaterManager } from '@/components/AutoUpdater'
import { useGlobalAuthGuard } from '@/composables/useAuthGuard'

const router = useRouter()

// 检查是否在Electron环境
const isElectron = typeof window !== 'undefined' && !!(window as any).electronAPI

// AutoUpdater 引用
const autoUpdaterRef = ref<InstanceType<typeof AutoUpdaterManager> | null>(null)

// 使用全局认证守卫
const {
  shouldShowApp,
  shouldShowLogin,
  shouldShowLoading,
  initAuthGuard,
} = useGlobalAuthGuard()

// 检查更新方法（供Navigation组件调用）
const checkForUpdates = () => {
  if (autoUpdaterRef.value && isElectron) {
    autoUpdaterRef.value.checkForUpdates()
  } else {
    console.warn('自动更新功能仅在Electron环境下可用')
  }
}

// 提供检查更新方法给子组件
provide('checkForUpdates', checkForUpdates)

// 处理强制导航到登录页的事件
const handleForceNavigateToLogin = (event: CustomEvent) => {
  const { reason } = event.detail
  console.log(`🔄 收到强制导航到登录页事件，原因: ${reason}`)

  try {
    router.push('/login')
    console.log('✅ 强制导航到登录页成功')
  } catch (error) {
    console.error('❌ 强制导航到登录页失败:', error)
    // 在Electron环境中，作为最后手段，可以尝试重新加载应用
    if (window.electronAPI) {
      console.log('🔄 尝试重新加载Electron应用')
      window.location.reload()
    }
  }
}

// 应用启动时检查认证状态
onMounted(() => {
  // 初始化认证守卫
  initAuthGuard()

  // 监听强制导航事件
  window.addEventListener('force-navigate-to-login', handleForceNavigateToLogin as EventListener)
})

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener('force-navigate-to-login', handleForceNavigateToLogin as EventListener)
})
</script>

<style>
body {
  overflow: hidden;
}
</style>