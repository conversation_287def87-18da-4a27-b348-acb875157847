<template>
  <div class="py-2">
    <div class="flex flex-col h-full">
      <!-- 固定头部区域 -->
      <div class="flex-shrink-0">
        <!-- 面包屑导航 -->
        <Breadcrumb class="px-2 pb-2 border-b" :breadcrumbs="breadcrumbs" :loading="isLoading" />

        <!-- 筛选器组件 -->
        <FilterList :filters="filterOptions" :loading="apiFiltersLoading" :default-values="defaultFilterValues"
          :required-fields="['category_id']" @filter-change="handleFilterChange" />

        <!-- 工具栏 -->
        <TrashToolsBar :file-count="trashData.total" @search="handleSearch" @refresh="handleRefresh" />
      </div>

      <!-- 可滚动的文件显示区域 -->
      <div class="p-2" style="height: calc(100vh - 256px)">
        <!-- 加载状态覆盖层 -->
        <div v-if="isLoading && !trashData.items.length" class="flex justify-center items-center h-full">
          <div class="flex flex-col items-center space-y-4">
            <div class="w-8 h-8 rounded-full border-b-2 animate-spin border-primary"></div>
            <p class="text-sm text-muted-foreground">正在加载回收站内容...</p>
          </div>
        </div>

        <!-- 文件显示区域 -->
        <TrashFilesView ref="filesViewRef" :items="displayItems" :view-mode="viewMode" :empty-icon="Trash2Icon"
          :empty-title="emptyTitle" :empty-message="emptyMessage" :api-filter-options="apiFilterOptions"
          :loading="isLoading" :page-size="pageSize" :category-id="categoryId" :total-items="trashData.total"
          :current-page="currentPage" @file-click="handleFileClick" @file-double-click="handleFileDoubleClick"
          @folder-click="handleFolderClick" @folder-double-click="handleFolderDoubleClick"
          @item-renamed="handleItemRenamed" @restore-success="handleRestoreSuccess"
          @delete-success="handleDeleteSuccess" @refresh-directory="handleRefreshDirectory"
          @page-change="handlePageChange" @page-size-change="handlePageSizeChange" />
      </div>

      <!-- 文件预览弹窗 -->
      <FilePreviewModal :visible="showPreview" :preview-url="currentPreviewFile?.preview || ''"
        :file-name="currentPreviewFile?.name" :file-type="currentPreviewFile?.mimeType" @close="closePreview" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Trash2, X } from 'lucide-vue-next'

import { useViewMode } from '@/composables/useViewMode'
import { useSidebarStore } from '@/store/sidebar'
import { formatFileSize } from '@/lib/upload-utils'
import api from '@/api'
import FilterList from './Folders/FilterList.vue'
import TrashToolsBar from './Trash/TrashToolsBar.vue'
import TrashFilesView from './Trash/TrashFilesView.vue'
import type { ItemType } from '@/types/files'
import Breadcrumb from '@/components/ui/breadcrumb/Breadcrumb.vue'
import { FilePreviewModal } from '@/components/Preview'
import { toast } from 'vue-sonner'

// 图标
const Trash2Icon = Trash2

// 使用统一的配置
const { viewMode } = useViewMode()
const sidebarStore = useSidebarStore()

// 路由和状态
const currentFilters = ref<Record<string, any>>({})
const searchQuery = ref('')

// 文件预览状态
const showPreview = ref(false)
const currentPreviewFile = ref<ItemType | null>(null)

// API 筛选项类型定义
interface ApiFilterOption {
  value: number | string
  name: string
}

interface ApiFilterGroup {
  field_name: string
  label: string
  type: 'select' | 'multiselect'
  upload_type: 'select' | 'multiselect'
  source: string
  options: ApiFilterOption[]
  is_form: string
  is_search: string
}

// 筛选项状态
const apiFilterOptions = ref<ApiFilterGroup[]>([])
const apiFiltersLoading = ref(false)
const lastLoadedCategoryId = ref<number | null>(null)

// 分页参数
const currentPage = ref(1)
const pageSize = ref(20)

// 目录内容加载状态锁
const isLoadingContents = ref(false)

// 组件初始化标记 - 移除未使用的变量

// 综合的加载状态
const isLoading = computed(() => {
  return isLoadingContents.value || apiFiltersLoading.value || sidebarStore.isLoading
})

// 当前回收站数据 - 参考getDirectoryContents的数据结构
const trashData = ref<{
  items: ItemType[];
  total: number;
  page: string;
  limit: string;
  pathData: any[];
  pathDataStr: string;
  categoryId: string;
  categoryKey: string;
}>({
  items: [],
  total: 0,
  page: '1',
  limit: '20',
  pathData: [],
  pathDataStr: '',
  categoryId: '',
  categoryKey: '',
})

// 动态的 categoryId - 基于当前选中的分类筛选
const categoryId = computed(() => {
  // 如果用户选择了分类筛选，使用选中的分类 ID
  if (currentFilters.value.category_id) {
    return currentFilters.value.category_id
  }
  // 否则使用默认的模型分类 ID
  const categoryFilter = apiFilterOptions.value.find(filter => filter.field_name === 'category_id')
  if (categoryFilter && categoryFilter.options) {
    const modelOption = categoryFilter.options.find((option: ApiFilterOption) => option.name === '模型')
    if (modelOption) {
      return String(modelOption.value)
    }
  }
  // 最后的回退值
  return '1'
})

// 构建筛选参数
const buildFilterParams = () => {
  const filters: Record<string, any> = {}

  // 添加搜索查询
  if (searchQuery.value.trim()) {
    filters.search_world = searchQuery.value.trim()
  }

  // 添加动态筛选字段
  Object.keys(currentFilters.value).forEach(fieldName => {
    const fieldValue = currentFilters.value[fieldName]

    // 跳过空值
    if (fieldValue === null || fieldValue === undefined) {
      return
    }

    // 处理数组类型的多选筛选
    if (Array.isArray(fieldValue) && fieldValue.length > 0) {
      filters[fieldName] = fieldValue
    }

    // 处理单选筛选
    if (!Array.isArray(fieldValue) && fieldValue !== null && fieldValue !== undefined) {
      filters[fieldName] = fieldValue
    }
  })

  return filters
}

// 加载筛选选项
const loadFilterOptions = async (categoryId: string, forceReload = false) => {
  if (!categoryId || apiFiltersLoading.value || (lastLoadedCategoryId.value === Number(categoryId) && !forceReload)) {
    return
  }

  apiFiltersLoading.value = true
  try {
    const numericCategoryId = Number(categoryId)
    if (isNaN(numericCategoryId)) {
      console.error('Invalid categoryId:', categoryId)
      return
    }

    // 同时请求三个接口
    const [gameListResponse, tagListResponse, topCategoriesResponse] = await Promise.all([
      api.tags.gameProjects.getGameProjectsList().catch(err => {
        console.warn('获取游戏列表失败:', err)
        return { code: -1, data: [] }
      }),
      api.tags.tags.getTagList().catch(err => {
        console.warn('获取标签列表失败:', err)
        return { code: -1, data: [] }
      }),
      api.common.getTopCategoryDirectories().catch(err => {
        console.warn('获取顶级分类失败:', err)
        return { code: -1, data: [] }
      })
    ])

    const filterGroups: any[] = []

    // 处理游戏项目数据
    if (gameListResponse.code === 0 && gameListResponse.data && Array.isArray(gameListResponse.data)) {
      const gameOptions = gameListResponse.data.map(game => ({
        value: game.id,
        name: game.game_name
      }))

      if (gameOptions.length > 0) {
        filterGroups.push({
          field_name: 'game_ids',
          label: '游戏项目',
          type: 'multiselect',
          upload_type: 'multiselect',
          source: 'api',
          options: gameOptions,
          is_form: 'Y',
          is_search: 'Y'
        })
      }
    }

    // 处理标签数据
    if (tagListResponse.code === 0 && tagListResponse.data && Array.isArray(tagListResponse.data)) {
      // 将所有标签扁平化处理
      const allTagOptions: any[] = []
      tagListResponse.data.forEach(category => {
        if (category.tags && Array.isArray(category.tags)) {
          category.tags.forEach(tag => {
            allTagOptions.push({
              value: tag.id,
              name: tag.item_value
            })
          })
        }
      })

      if (allTagOptions.length > 0) {
        filterGroups.push({
          field_name: 'tag',
          label: '标签',
          type: 'multiselect',
          upload_type: 'multiselect',
          source: 'api',
          options: allTagOptions,
          is_form: 'Y',
          is_search: 'Y'
        })
      }
    }

    // 处理顶级分类数据
    if (topCategoriesResponse.code === 0 && topCategoriesResponse.data && Array.isArray(topCategoriesResponse.data)) {
      const categoryOptions = topCategoriesResponse.data.map(category => ({
        value: category.category_id || category.id,
        name: category.name || category.category_name
      }))

      if (categoryOptions.length > 0) {
        filterGroups.push({
          field_name: 'category_id',
          label: '分类',
          type: 'select',
          upload_type: 'select',
          source: 'api',
          options: categoryOptions,
          is_form: 'Y',
          is_search: 'Y'
        })
      }
    }



    apiFilterOptions.value = filterGroups
    lastLoadedCategoryId.value = numericCategoryId
  } catch (error) {
    console.error('加载筛选选项失败:', error)
    apiFilterOptions.value = []
    lastLoadedCategoryId.value = null
  } finally {
    apiFiltersLoading.value = false
  }
}

// 加载回收站内容
const loadTrashContents = async (_reason?: string) => {
  // 防止重复调用
  if (isLoadingContents.value) {
    return
  }

  const currentCategoryId = categoryId.value
  if (!currentCategoryId) {
    console.error('categoryId is required')
    return
  }

  isLoadingContents.value = true

  try {
    // 确保 sidebar 数据已加载（只在未加载时才调用）
    if (!sidebarStore.isLoaded && !sidebarStore.isLoading) {
      await sidebarStore.fetchSidebarData()
    } else if (sidebarStore.isLoading) {
      // 如果正在加载中，等待加载完成
      while (sidebarStore.isLoading) {
        await new Promise(resolve => setTimeout(resolve, 50))
      }
    }

    // 加载筛选选项（这里会设置默认的"模型"分类筛选）
    await loadFilterOptions(currentCategoryId)

    // 构建筛选参数
    const filters = buildFilterParams()

    // 构建请求参数 - 参考getDirectoryContents的方式
    const requestData = {
      category_id: currentCategoryId,
      page: currentPage.value,
      limit: pageSize.value,
      ...filters, // 包含筛选参数
    }

    // 调用回收站API - 这里是唯一的不同点
    const response = await api.files.getRecycleBinList(requestData)

    if (response.code === 0 && response.data) {

      // 直接使用API返回的数据结构，与getDirectoryContents保持一致
      const result = {
        items: (response.data.data || []).map((item: any) => {
          if (!item) return null

          const modifiedDate = new Date(item.deleted_at)

          if (item.is_folder === 1) {
            // 文件夹
            return {
              id: item.id?.toString() || '',
              name: item.file_name || '',
              type: "folder" as const,
              itemCount: 0,
              modifiedAt: modifiedDate,
              // uploader: item.update_user_name || item.update_user || '',
              creator: item.create_user_name || '',
              deleter: item.update_user_name || '',
              category_id: item.category_id || 0,
              thumbnailSmall: item.thumbnail_small || '',
              thumbnailMedium: item.thumbnail_medium || '',
              preview: item.preview || '',
              category_fields: item.category_fields || {},
            }
          } else {
            // 文件
            const getFileTypeFromMimeType = (mimeType: string): string => {
              if (!mimeType) return "file"
              if (mimeType.startsWith("image/")) return "image"
              if (mimeType.startsWith("video/")) return "video"
              if (mimeType.startsWith("audio/")) return "audio"
              if (mimeType.includes("pdf")) return "pdf"
              return "file"
            }

            const fileType = getFileTypeFromMimeType(item.mime_type || '')

            return {
              id: item.id?.toString() || '',
              name: item.file_name || '',
              size: item.size_human || formatFileSize(item.size) || '',
              type: fileType,
              modifiedAt: modifiedDate,
              creator: item.create_user_name || '',
              deleter: item.update_user_name || '',
              category_id: item.category_id || 0,
              mimeType: item.mime_type || '',
              version: item.ver || '',
              thumbnailSmall: item.thumbnail_small || '',
              thumbnailMedium: item.thumbnail_medium || '',
              preview: item.preview || '',
              format: item.format || '',
              rawSize: item.size || 0,
              hasAnimation: item.has_animation === "Y",
              hasUeProject: item.has_ue_project === "Y",
              gameProject: item.game_project || '',
              class: item.class || '',
              tag: item.tag || '',
              category_fields: item.category_fields || {},
            }
          }
        }).filter(Boolean), // 过滤掉null值
        total: response.data.total || 0,
        page: response.data.page || currentPage.value.toString(),
        limit: response.data.limit || pageSize.value.toString(),
        pathData: [],
        pathDataStr: "",
        categoryId: currentCategoryId.toString(),
        categoryKey: "",
      }

      trashData.value = result
      // console.log(`回收站内容加载完成 (原因: ${reason || '未知'})`)
    } else {
      console.error('API返回错误:', response.msg)
      trashData.value = {
        items: [],
        total: 0,
        page: currentPage.value.toString(),
        limit: pageSize.value.toString(),
        pathData: [],
        pathDataStr: "",
        categoryId: currentCategoryId.toString(),
        categoryKey: "",
      }
    }
  } catch (err) {
    // console.error('加载回收站内容失败:', err)
    trashData.value = {
      items: [],
      total: 0,
      page: currentPage.value.toString(),
      limit: pageSize.value.toString(),
      pathData: [],
      pathDataStr: "",
      categoryId: categoryId.value,
      categoryKey: "",
    }
  } finally {
    isLoadingContents.value = false
  }
}

// 面包屑导航
const breadcrumbs = computed(() => {
  return [
    {
      name: '回收站',
      path: '/trash',
      icon: Trash2,
      iconColor: 'text-orange-500'
    }
  ]
})

// 筛选器配置
const filterOptions = computed(() => {
  return apiFilterOptions.value.map(apiFilter => ({
    field_name: apiFilter.field_name,
    label: apiFilter.label,
    type: apiFilter.type,
    options: apiFilter.options.map(option => ({
      name: option.name,
      // 根据字段类型决定是否转换
      value: apiFilter.field_name === 'category_id' ? String(option.value) : option.value
    })),
    is_form: apiFilter.is_form,
    is_search: apiFilter.is_search
  }))
})

// 默认筛选值 - 自动选中"模型"分类
const defaultFilterValues = computed(() => {
  const categoryFilter = apiFilterOptions.value.find(filter => filter.field_name === 'category_id')
  if (categoryFilter && categoryFilter.options) {
    const modelOption = categoryFilter.options.find((option: ApiFilterOption) => option.name === '模型')
    if (modelOption) {
      return {
        category_id: String(modelOption.value)
      }
    }
  }
  return {}
})

// 筛选器变化处理
const handleFilterChange = (filters: Record<string, any>) => {
  currentFilters.value = filters
  currentPage.value = 1
  loadTrashContents('筛选器变化')
}

// 处理搜索
const handleSearch = (query: string) => {
  searchQuery.value = query
  currentPage.value = 1
  loadTrashContents('搜索查询')
}

// 处理刷新
const handleRefresh = () => {
  console.log('刷新回收站列表')
  loadTrashContents('手动刷新')
}

// 处理分页变化 - 参考getDirectoryContents的方式
const handlePageChange = (page: number) => {
  currentPage.value = page
  loadTrashContents('分页变化')
}

// 处理页大小变化 - 参考getDirectoryContents的方式
const handlePageSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadTrashContents('页大小变化')
}

// 空状态显示文案
const emptyTitle = computed(() => {
  if (searchQuery.value.trim()) {
    return '没有找到匹配的文件'
  }
  return '回收站为空'
})

const emptyMessage = computed(() => {
  if (searchQuery.value.trim()) {
    return '尝试调整搜索条件'
  }
  return '没有找到已删除的文件'
})

// FilesView 组件引用
const filesViewRef = ref()

// 显示项列表
const displayItems = computed(() => {
  return trashData.value.items
})

// 处理文件夹点击
const handleFolderClick = (item: ItemType) => {
  console.log('选择文件夹:', item.name)
  // 回收站中不能进入文件夹
}

// 处理文件点击
const handleFileClick = (item: ItemType) => {
  console.log('文件单击:', item.name)
}

// 处理文件双击
const handleFileDoubleClick = (_item: ItemType) => {
  // 回收站不支持文件预览，显示带红色X的提示
  toast.error('回收站暂时不支持查看，请还原后再试', {
    icon: X,
    style: {
      borderColor: '#ef4444',
      color: '#dc2626'
    }
  })
}

// 处理文件夹双击
const handleFolderDoubleClick = (_item: ItemType) => {
  // 回收站不支持进入文件夹，显示带红色X的提示
  toast.error('回收站暂时不支持查看，请还原后再试', {
    icon: X,
    style: {
      borderColor: '#ef4444',
      color: '#dc2626'
    }
  })
}

// 关闭预览
const closePreview = () => {
  showPreview.value = false
  currentPreviewFile.value = null
}

// 处理项目重命名
const handleItemRenamed = (_itemId: string, _newName: string, _item: ItemType) => {
  // 回收站中不支持重命名
  console.log('回收站中不支持重命名')
}

// 处理还原成功
const handleRestoreSuccess = (restoredItems: ItemType[]) => {
  console.log('还原成功:', restoredItems)
  // 从本地列表中移除已还原的项目
  trashData.value.items = trashData.value.items.filter(item =>
    !restoredItems.some(restored => restored.id === item.id)
  )
  trashData.value.total = Math.max(0, trashData.value.total - restoredItems.length)

  // 如果当前页没有数据了，且不是第一页，回到上一页
  if (trashData.value.items.length === 0 && currentPage.value > 1) {
    currentPage.value = Math.max(1, currentPage.value - 1)
    loadTrashContents('页面调整')
  }
}

// 处理删除成功
const handleDeleteSuccess = (deletedItems: ItemType[]) => {
  console.log('彻底删除成功:', deletedItems)
  // 从本地列表中移除已删除的项目
  trashData.value.items = trashData.value.items.filter(item =>
    !deletedItems.some(deleted => deleted.id === item.id)
  )
  trashData.value.total = Math.max(0, trashData.value.total - deletedItems.length)

  // 如果当前页没有数据了，且不是第一页，回到上一页
  if (trashData.value.items.length === 0 && currentPage.value > 1) {
    currentPage.value = Math.max(1, currentPage.value - 1)
    loadTrashContents('页面调整')
  }
}

// 刷新目录
const handleRefreshDirectory = () => {
  console.log('刷新回收站目录')
  loadTrashContents('刷新目录')
}

// 监听初始化加载 - 参考FolderView的watch实现
watch(() => true, (_newValue, oldValue) => {
  // 首次加载时才加载
  if (!oldValue) {
    loadTrashContents('初始化加载')
  }
}, { immediate: true })

// 移除onMounted，改用watch实现
</script>

<style scoped>
/* 自定义滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 4px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 4px;
  transition: background-color 0.2s;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--border) / 0.8);
}

/* 确保滚动区域有足够的内边距 */
.overflow-y-auto {
  padding-bottom: 1rem;
}
</style>