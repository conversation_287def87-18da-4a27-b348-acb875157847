<template>
    <div v-if="selectedCount > 0" class="flex gap-2 items-center" :class="containerClass">
        <TooltipProvider>
            <Tooltip>
                <TooltipTrigger as-child>
                    <Button variant="outline" size="sm" :disabled="isRestoring" @click.stop="handleRestore">
                        <RotateCcw class="w-4 h-4" /> {{ isRestoring ? '还原中...' : '还原' }}
                    </Button>
                </TooltipTrigger>
                <TooltipContent>
                    <p>{{ isRestoring ? '还原中...' : `还原 (${selectedCount} 项)` }}</p>
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
            <Tooltip>
                <TooltipTrigger as-child>
                    <Button variant="destructive" size="sm" :disabled="isPermanentlyDeleting"
                        @click.stop="handlePermanentlyDelete">
                        <Trash2 class="w-4 h-4" /> {{ isPermanentlyDeleting ? '删除中...' : '彻底删除' }}
                    </Button>
                </TooltipTrigger>
                <TooltipContent>
                    <p>{{ isPermanentlyDeleting ? '删除中...' : `彻底删除 (${selectedCount} 项)` }}</p>
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { RotateCcw, Trash2 } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { toast } from 'vue-sonner'
import api from '@/api'
import type { ItemType } from '@/types/files'

// Props
const props = defineProps<{
    selectedCount: number
    containerClass?: string
    selectedItems?: ItemType[]
    categoryId: string
}>()

// Emits
const emit = defineEmits<{
    restore: []
    permanentlyDelete: []
    restoreSuccess: [restoredItems: ItemType[]]
    deleteSuccess: [deletedItems: ItemType[]]
    refreshDirectory: []
}>()

// 状态
const isRestoring = ref(false)
const isPermanentlyDeleting = ref(false)

// 统一的还原API调用
const callRestoreAPI = async (fileIds: (number | string)[]): Promise<{ success: boolean; message: string }> => {
    try {
        const restoreData = {
            category_id: props.categoryId,
            file_ids: fileIds,
        }

        const response = await api.files.restoreFile(restoreData)

        if (response.code === 0) {
            return { success: true, message: response.msg || "还原成功" }
        } else {
            return { success: false, message: response.msg || "还原失败" }
        }
    } catch (error) {
        console.error("还原API调用失败:", error)
        const errorMessage = error instanceof Error ? error.message : "还原失败"
        return { success: false, message: errorMessage }
    }
}

// 统一的彻底删除API调用
const callPermanentlyDeleteAPI = async (fileIds: (number | string)[]): Promise<{ success: boolean; message: string }> => {
    try {
        const deleteData = {
            category_id: props.categoryId,
            file_ids: fileIds,
        }

        const response = await api.files.permanentlyDeleteFile(deleteData)

        if (response.code === 0) {
            return { success: true, message: response.msg || "彻底删除成功" }
        } else {
            return { success: false, message: response.msg || "彻底删除失败" }
        }
    } catch (error) {
        console.error("彻底删除API调用失败:", error)
        const errorMessage = error instanceof Error ? error.message : "彻底删除失败"
        return { success: false, message: errorMessage }
    }
}

// 还原文件
const handleRestore = async () => {
    if (!props.selectedItems || props.selectedItems.length === 0) {
        toast.warning("请选择要还原的文件")
        return
    }

    const items = props.selectedItems
    const itemCount = items.length

    // 生成确认信息
    const itemNames = itemCount <= 5
        ? items.map(item => item.name).join(", ")
        : `${items.slice(0, 3).map(item => item.name).join(", ")} 等 ${itemCount} 个项目`

    const confirmed = confirm(`确定要还原 ${itemCount} 个项目吗？\n${itemNames}`)
    if (!confirmed) {
        return
    }

    isRestoring.value = true

    // 显示进度
    const progressToastId = itemCount > 1 ? toast.loading(`正在还原 ${itemCount} 个项目...`) : null

    try {
        const fileIds = items.map(item => item.id)
        const result = await callRestoreAPI(fileIds)

        if (progressToastId) {
            toast.dismiss(progressToastId)
        }

        if (result.success) {
            const successMessage = result.message || `还原完成！成功还原 ${itemCount} 个项目`
            toast.success(successMessage)
            emit('restoreSuccess', items)
            emit('refreshDirectory')
        } else {
            toast.error(result.message)
        }
    } catch (error) {
        if (progressToastId) {
            toast.dismiss(progressToastId)
        }
        console.error('还原操作失败:', error)
        const errorMessage = error instanceof Error ? error.message : "还原操作失败"
        toast.error(errorMessage)
    } finally {
        isRestoring.value = false
    }
}

// 彻底删除文件
const handlePermanentlyDelete = async () => {
    if (!props.selectedItems || props.selectedItems.length === 0) {
        toast.warning("请选择要删除的文件")
        return
    }

    const items = props.selectedItems
    const itemCount = items.length

    // 生成确认信息
    const itemNames = itemCount <= 5
        ? items.map(item => item.name).join(", ")
        : `${items.slice(0, 3).map(item => item.name).join(", ")} 等 ${itemCount} 个项目`

    const confirmed = confirm(`确定要彻底删除 ${itemCount} 个项目吗？\n${itemNames}\n\n此操作无法撤销。`)
    if (!confirmed) {
        return
    }

    isPermanentlyDeleting.value = true

    // 显示进度
    const progressToastId = itemCount > 1 ? toast.loading(`正在彻底删除 ${itemCount} 个项目...`) : null

    try {
        const fileIds = items.map(item => item.id)
        const result = await callPermanentlyDeleteAPI(fileIds)

        if (progressToastId) {
            toast.dismiss(progressToastId)
        }

        if (result.success) {
            const successMessage = result.message || `彻底删除完成！成功删除 ${itemCount} 个项目`
            toast.success(successMessage)
            emit('deleteSuccess', items)
            emit('refreshDirectory')
        } else {
            toast.error(result.message)
        }
    } catch (error) {
        if (progressToastId) {
            toast.dismiss(progressToastId)
        }
        console.error('彻底删除操作失败:', error)
        const errorMessage = error instanceof Error ? error.message : "彻底删除操作失败"
        toast.error(errorMessage)
    } finally {
        isPermanentlyDeleting.value = false
    }
}
</script>