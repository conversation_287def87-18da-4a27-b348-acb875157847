<template>
    <Teleport to="body">
        <div v-if="show" :style="{ top: y + 'px', left: x + 'px' }"
            class="fixed z-50 py-1 border rounded-lg shadow-lg bg-popover border-border min-w-[140px]" @click.stop>
            <button v-for="action in actions" :key="action.label" @click="handleActionClick(action)"
                class="flex items-center px-4 py-2 w-full text-left transition-colors hover:bg-accent" :class="{
                    'text-destructive hover:bg-destructive/10': action.destructive,
                    'opacity-50 cursor-not-allowed disabled:hover:bg-transparent': action.disabled
                }" :disabled="action.disabled">
                <component :is="action.icon" class="mr-2 w-4 h-4" />
                {{ action.label }}
            </button>
        </div>
    </Teleport>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { RotateCcw, Trash2 } from 'lucide-vue-next'
import { toast } from 'vue-sonner'
import api from '@/api'
import type { ItemType } from '@/types/files'

// Props
const props = defineProps<{
    show: boolean
    x: number
    y: number
    item: ItemType | null
    selectedItems?: ItemType[]
    categoryId: string
}>()

// Emits
const emit = defineEmits<{
    close: []
    restore: [item: ItemType]
    batchRestore: [items: ItemType[]]
    permanentlyDelete: [item: ItemType]
    batchPermanentlyDelete: [items: ItemType[]]
    restoreSuccess: [items: ItemType[]]
    deleteSuccess: [items: ItemType[]]
    refreshDirectory: []
}>()

// 状态
const isRestoring = ref(false)
const isPermanentlyDeleting = ref(false)

// 计算实际要操作的文件列表
const getOperationItems = (): ItemType[] => {
    // 如果有多个文件选中，且右键点击的文件在选中列表中，则操作所有选中的文件
    if (props.selectedItems && props.selectedItems.length > 1 && props.item) {
        const isClickedItemSelected = props.selectedItems.some(item => item.id === props.item!.id)
        if (isClickedItemSelected) {
            return props.selectedItems
        }
    }
    // 否则只操作右键点击的文件
    return props.item ? [props.item] : []
}

// 菜单操作定义
interface MenuAction {
    label: string
    icon: any
    handler: string
    destructive?: boolean
    disabled?: boolean
}

// 计算可用的菜单操作
const actions = computed<MenuAction[]>(() => {
    if (!props.item) return []

    const operationItems = getOperationItems()
    const isMultiple = operationItems.length > 1

    return [
        {
            label: isRestoring.value ? '还原中...' : (isMultiple ? `还原 (${operationItems.length} 项)` : '还原'),
            icon: RotateCcw,
            handler: 'restore',
            disabled: isRestoring.value
        },
        {
            label: isPermanentlyDeleting.value ? '删除中...' : (isMultiple ? `彻底删除 (${operationItems.length} 项)` : '彻底删除'),
            icon: Trash2,
            handler: 'permanentlyDelete',
            destructive: true,
            disabled: isPermanentlyDeleting.value
        }
    ]
})

// 处理菜单点击
const handleActionClick = (action: MenuAction) => {
    if (action.disabled) return

    const operationItems = getOperationItems()
    const isMultiple = operationItems.length > 1

    switch (action.handler) {
        case 'restore':
            if (isMultiple) {
                handleBatchRestore(operationItems)
            } else {
                handleRestore(operationItems[0])
            }
            break
        case 'permanentlyDelete':
            if (isMultiple) {
                handleBatchPermanentlyDelete(operationItems)
            } else {
                handlePermanentlyDelete(operationItems[0])
            }
            break
    }

    emit('close')
}

// 还原单个文件
const handleRestore = async (item: ItemType) => {
    emit('restore', item)
    await handleRestoreItems([item])
}

// 批量还原文件
const handleBatchRestore = async (items: ItemType[]) => {
    emit('batchRestore', items)
    await handleRestoreItems(items)
}

// 还原文件的通用方法
const handleRestoreItems = async (items: ItemType[]) => {
    const itemCount = items.length

    // 生成确认信息
    const itemNames = itemCount <= 5
        ? items.map(item => item.name).join(", ")
        : `${items.slice(0, 3).map(item => item.name).join(", ")} 等 ${itemCount} 个项目`

    const confirmed = confirm(`确定要还原 ${itemCount} 个项目吗？\n${itemNames}`)
    if (!confirmed) {
        return
    }

    isRestoring.value = true

    // 显示进度
    const progressToastId = itemCount > 1 ? toast.loading(`正在还原 ${itemCount} 个项目...`) : null

    try {
        const restoreData = {
            category_id: props.categoryId,
            file_ids: items.map(item => item.id),
        }

        const response = await api.files.restoreFile(restoreData)

        if (progressToastId) {
            toast.dismiss(progressToastId)
        }

        if (response.code === 0) {
            const successMessage = response.msg || `还原完成！成功还原 ${itemCount} 个项目`
            toast.success(successMessage)
            emit('restoreSuccess', items)
            emit('refreshDirectory')
        } else {
            toast.error(response.msg || '还原失败')
        }
    } catch (error) {
        if (progressToastId) {
            toast.dismiss(progressToastId)
        }
        console.error('还原操作失败:', error)
        const errorMessage = error instanceof Error ? error.message : "还原操作失败"
        toast.error(errorMessage)
    } finally {
        isRestoring.value = false
    }
}

// 彻底删除单个文件
const handlePermanentlyDelete = async (item: ItemType) => {
    emit('permanentlyDelete', item)
    await handlePermanentlyDeleteItems([item])
}

// 批量彻底删除文件
const handleBatchPermanentlyDelete = async (items: ItemType[]) => {
    emit('batchPermanentlyDelete', items)
    await handlePermanentlyDeleteItems(items)
}

// 彻底删除文件的通用方法
const handlePermanentlyDeleteItems = async (items: ItemType[]) => {
    const itemCount = items.length

    // 生成确认信息
    const itemNames = itemCount <= 5
        ? items.map(item => item.name).join(", ")
        : `${items.slice(0, 3).map(item => item.name).join(", ")} 等 ${itemCount} 个项目`

    const confirmed = confirm(`确定要彻底删除 ${itemCount} 个项目吗？\n${itemNames}\n\n此操作无法撤销。`)
    if (!confirmed) {
        return
    }

    isPermanentlyDeleting.value = true

    // 显示进度
    const progressToastId = itemCount > 1 ? toast.loading(`正在彻底删除 ${itemCount} 个项目...`) : null

    try {
        const deleteData = {
            category_id: props.categoryId,
            file_ids: items.map(item => item.id),
        }

        const response = await api.files.permanentlyDeleteFile(deleteData)

        if (progressToastId) {
            toast.dismiss(progressToastId)
        }

        if (response.code === 0) {
            const successMessage = response.msg || `彻底删除完成！成功删除 ${itemCount} 个项目`
            toast.success(successMessage)
            emit('deleteSuccess', items)
            emit('refreshDirectory')
        } else {
            toast.error(response.msg || '彻底删除失败')
        }
    } catch (error) {
        if (progressToastId) {
            toast.dismiss(progressToastId)
        }
        console.error('彻底删除操作失败:', error)
        const errorMessage = error instanceof Error ? error.message : "彻底删除操作失败"
        toast.error(errorMessage)
    } finally {
        isPermanentlyDeleting.value = false
    }
}
</script>