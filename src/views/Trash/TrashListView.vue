<template>
    <div class="list-view">
        <Table>
            <!-- 表头 -->
            <TableHeader>
                <TableRow>
                    <TableHead class="w-16">
                        <div class="flex gap-2 items-center">
                            <Checkbox :model-value="checkboxState" @update:model-value="toggleSelectAll" />
                            <span class="hidden text-xs text-muted-foreground sm:inline">
                                {{ selectedItems.size > 0 ? `${selectedItems.size}/${items.length}` : '' }}
                            </span>
                        </div>
                    </TableHead>
                    <TableHead class="min-w-0">
                        <div class="flex items-center">
                            <!-- 排序按钮 -->
                            <Button variant="ghost" size="sm"
                                class="justify-start p-0 h-auto font-medium hover:bg-transparent"
                                @click="handleSort('name')">
                                文件名
                                <ChevronUp v-if="sortBy === 'name' && sortOrder === 'asc'" class="ml-1 w-4 h-4" />
                                <ChevronDown v-else-if="sortBy === 'name' && sortOrder === 'desc'"
                                    class="ml-1 w-4 h-4" />
                                <ChevronsUpDown v-else class="ml-1 w-4 h-4 opacity-50" />
                            </Button>

                            <!-- 操作按钮组 -->
                            <TrashActionButtonGroup :selected-count="selectedItems.size"
                                :selected-items="selectedItemsArray" :category-id="categoryId"
                                container-class="gap-1 ml-10" @restore="handleRestore"
                                @permanently-delete="handlePermanentlyDelete" @restore-success="handleRestoreSuccess"
                                @delete-success="handleDeleteSuccess" @refresh-directory="handleRefreshDirectory" />
                        </div>
                    </TableHead>
                    <TableHead class="hidden w-28 md:table-cell">
                        <Button variant="ghost" size="sm"
                            class="justify-start p-0 h-auto font-medium hover:bg-transparent"
                            @click="handleSort('creator')">
                            上传者
                            <ChevronUp v-if="sortBy === 'creator' && sortOrder === 'asc'" class="ml-1 w-4 h-4" />
                            <ChevronDown v-else-if="sortBy === 'creator' && sortOrder === 'desc'"
                                class="ml-1 w-4 h-4" />
                            <ChevronsUpDown v-else class="ml-1 w-4 h-4 opacity-50" />
                        </Button>
                    </TableHead>
                    <TableHead class="hidden w-28 md:table-cell">
                        <Button variant="ghost" size="sm"
                            class="justify-start p-0 h-auto font-medium hover:bg-transparent"
                            @click="handleSort('deleter')">
                            删除者
                            <ChevronUp v-if="sortBy === 'deleter' && sortOrder === 'asc'" class="ml-1 w-4 h-4" />
                            <ChevronDown v-else-if="sortBy === 'deleter' && sortOrder === 'desc'"
                                class="ml-1 w-4 h-4" />
                            <ChevronsUpDown v-else class="ml-1 w-4 h-4 opacity-50" />
                        </Button>
                    </TableHead>
                    <TableHead class="hidden w-24 sm:table-cell">
                        <Button variant="ghost" size="sm"
                            class="justify-start p-0 h-auto font-medium hover:bg-transparent"
                            @click="handleSort('size')">
                            大小
                            <ChevronUp v-if="sortBy === 'size' && sortOrder === 'asc'" class="ml-1 w-4 h-4" />
                            <ChevronDown v-else-if="sortBy === 'size' && sortOrder === 'desc'" class="ml-1 w-4 h-4" />
                            <ChevronsUpDown v-else class="ml-1 w-4 h-4 opacity-50" />
                        </Button>
                    </TableHead>
                    <TableHead class="w-36">
                        <Button variant="ghost" size="sm"
                            class="justify-start p-0 h-auto font-medium hover:bg-transparent"
                            @click="handleSort('modifiedAt')">
                            删除时间
                            <ChevronUp v-if="sortBy === 'modifiedAt' && sortOrder === 'asc'" class="ml-1 w-4 h-4" />
                            <ChevronDown v-else-if="sortBy === 'modifiedAt' && sortOrder === 'desc'"
                                class="ml-1 w-4 h-4" />
                            <ChevronsUpDown v-else class="ml-1 w-4 h-4 opacity-50" />
                        </Button>
                    </TableHead>
                </TableRow>
            </TableHeader>

            <!-- 表格内容 -->
            <TableBody>
                <TableRow v-for="item in sortedItems" :key="item.id" :data-id="item.id"
                    :class="{ 'bg-muted/50': selectedItems.has(item.id) }"
                    class="transition-colors cursor-pointer hover:bg-muted/30" @click="handleItemClick(item)"
                    @dblclick="handleItemDoubleClick(item)" @contextmenu.prevent="handleItemContextMenu($event, item)">
                    <!-- 多选框 -->
                    <TableCell class="w-12">
                        <Checkbox :model-value="selectedItems.has(item.id)" @click.stop
                            @update:model-value="(value: boolean | 'indeterminate') => toggleItemSelect(item.id, value === true)" />
                    </TableCell>

                    <!-- 文件名（带图标） -->
                    <TableCell class="min-w-0">
                        <div class="flex gap-3 items-center">
                            <div class="file-icon">
                                <Folder v-if="item.type === 'folder'" class="w-4 h-4 text-blue-500" />
                                <img v-else-if="item.thumbnailMedium" class="h-full" :src="item.thumbnailMedium"
                                    :alt="item.name">
                                <component v-else :is="getFileTypeIcon(item.type)" class="w-4 h-4"
                                    :class="getFileTypeColor(item.type)" />
                            </div>
                            <!-- 文件名 -->
                            <div class="flex-1 min-w-0">
                                <span class="font-medium truncate">{{ item.name }}</span>
                            </div>
                        </div>
                    </TableCell>

                    <!-- 上传者 -->
                    <TableCell class="hidden w-28 md:table-cell text-muted-foreground">
                        <span class="truncate">{{ getCreator(item) }}</span>
                    </TableCell>

                    <!-- 删除者 -->
                    <TableCell class="hidden w-28 md:table-cell text-muted-foreground">
                        <span class="truncate">{{ getDeleter(item) }}</span>
                    </TableCell>

                    <!-- 大小 -->
                    <TableCell class="hidden w-24 sm:table-cell text-muted-foreground">
                        <span class="text-sm">
                            <template v-if="item.type === 'folder'">
                                /
                            </template>
                            <template v-else>
                                {{ formatSize(item.size) }}
                            </template>
                        </span>
                    </TableCell>

                    <!-- 删除时间 -->
                    <TableCell class="w-36 text-muted-foreground">
                        <span class="truncate">{{ formatDate(item.modifiedAt) }}</span>
                    </TableCell>
                </TableRow>

                <!-- 空状态 -->
                <TableRow v-if="props.items.length === 0">
                    <TableCell colspan="6" class="py-8 text-center text-muted-foreground">
                        暂无数据
                    </TableCell>
                </TableRow>
            </TableBody>
        </Table>
    </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { ChevronUp, ChevronDown, ChevronsUpDown, Folder } from 'lucide-vue-next'
import TrashActionButtonGroup from './TrashActionButtonGroup.vue'
import { useFolderConfig } from '@/composables/useFolderConfig'
import type { ItemType, FileItemType, FolderItemType } from '@/types/files'

// Props
const props = defineProps<{
    items: ItemType[]
    selectedItems?: Set<string>
    categoryId: string
}>()

// 监听items变化，重置排序状态
watch(
    () => props.items,
    () => {
        // 当数据更新时，重置排序状态，保持后端顺序
        sortBy.value = null
        userHasSorted.value = false
    },
    { deep: true }
)

// Emits
const emit = defineEmits<{
    fileClick: [item: FileItemType]
    fileDoubleClick: [item: FileItemType]
    folderClick: [item: FolderItemType]
    folderDoubleClick: [item: FolderItemType]
    contextmenu: [event: MouseEvent, item: ItemType]
    selectionChange: [selectedIds: Set<string>]
    restore: [selectedIds: Set<string>]
    permanentlyDelete: [selectedIds: Set<string>]
    rename: [itemId: string, newName: string, item: ItemType]
    refreshDirectory: []
}>()

// 获取文件类型图标配置
const { getFileTypeIcon, getFileTypeColor } = useFolderConfig()

// 选择状态 - 使用父组件传入的选择状态或本地状态
const selectedItems = computed(() => props.selectedItems || new Set<string>())

// 计算选中的项目数组
const selectedItemsArray = computed(() => {
    return props.items.filter(item => selectedItems.value.has(item.id))
})

// 排序状态
const sortBy = ref<'name' | 'creator' | 'deleter' | 'size' | 'modifiedAt' | null>(null) // 默认不排序
const sortOrder = ref<'asc' | 'desc'>('asc')
const userHasSorted = ref(false) // 标记用户是否主动排序

// 排序后的项目
const sortedItems = computed(() => {
    const items = [...props.items]

    // 如果用户没有主动排序，保持后端原始顺序
    if (!userHasSorted.value || !sortBy.value) {
        return items
    }

    items.sort((a, b) => {
        let aValue: any
        let bValue: any

        switch (sortBy.value) {
            case 'name':
                aValue = (a.name || '').toLowerCase()
                bValue = (b.name || '').toLowerCase()
                break
            case 'creator':
                aValue = (getCreator(a) || '').toLowerCase()
                bValue = (getCreator(b) || '').toLowerCase()
                break
            case 'deleter':
                aValue = (getDeleter(a) || '').toLowerCase()
                bValue = (getDeleter(b) || '').toLowerCase()
                break
            case 'size':
                aValue = a.size || 0
                bValue = b.size || 0
                break
            case 'modifiedAt':
                aValue = a.modifiedAt.getTime()
                bValue = b.modifiedAt.getTime()
                break
            default:
                return 0
        }

        if (aValue < bValue) return sortOrder.value === 'asc' ? -1 : 1
        if (aValue > bValue) return sortOrder.value === 'asc' ? 1 : -1
        return 0
    })

    return items
})

// 计算 Checkbox 的三种状态
const checkboxState = computed<boolean | 'indeterminate'>(() => {
    if (props.items.length === 0) return false
    if (selectedItems.value.size === 0) return false
    if (selectedItems.value.size === props.items.length) return true
    return 'indeterminate'
})

// 处理排序
const handleSort = (column: 'name' | 'creator' | 'deleter' | 'size' | 'modifiedAt') => {
    if (sortBy.value === column) {
        sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
    } else {
        sortBy.value = column
        sortOrder.value = 'asc'
    }
    userHasSorted.value = true // 标记用户主动排序
}

// 全选/取消全选
const toggleSelectAll = (checked: boolean | 'indeterminate') => {
    if (checked === true) {
        const allIds = new Set(props.items.map(item => item.id))
        emit('selectionChange', allIds)
    } else {
        emit('selectionChange', new Set())
    }
}

// 处理项目选择
const toggleItemSelect = (id: string, selected: boolean) => {
    const newSelected = new Set(selectedItems.value)
    if (selected) {
        newSelected.add(id)
    } else {
        newSelected.delete(id)
    }
    emit('selectionChange', newSelected)
}

// 处理项目点击
const handleItemClick = (item: ItemType) => {
    if (item.type === 'folder') {
        emit('folderClick', item as FolderItemType)
    } else {
        emit('fileClick', item as FileItemType)
    }
}

// 处理项目双击
const handleItemDoubleClick = (item: ItemType) => {
    if (item.type === 'folder') {
        emit('folderDoubleClick', item as FolderItemType)
    } else {
        emit('fileDoubleClick', item as FileItemType)
    }
}

// 处理右键菜单
const handleItemContextMenu = (event: MouseEvent, item: ItemType) => {
    emit('contextmenu', event, item)
}

// 处理还原操作
const handleRestore = () => {
    if (selectedItems.value.size === 0) return
    emit('restore', selectedItems.value)
}

// 处理彻底删除操作
const handlePermanentlyDelete = () => {
    if (selectedItems.value.size === 0) return
    emit('permanentlyDelete', selectedItems.value)
}

// 处理还原成功
const handleRestoreSuccess = (_restoredItems: ItemType[]) => {
    // 清除选择
    // emit('selectionChange', new Set())
}

// 处理删除成功
const handleDeleteSuccess = (_deletedItems: ItemType[]) => {
    // 清除选择
    // emit('selectionChange', new Set())
}

// 处理刷新目录
const handleRefreshDirectory = () => {
    emit('refreshDirectory')
}

// 工具函数
// const getUploader = (item: ItemType): string => {
//     return (item as any).uploader || '未知'
// }

const getCreator = (item: ItemType): string => {
    // 尝试多种可能的字段名
    return (item as any).creator || (item as any).create_user_name || '未知'
}

const getDeleter = (item: ItemType): string => {
    // 尝试多种可能的字段名
    return (item as any).deleter || (item as any).update_user_name || '未知'
}

const formatSize = (size: number | string | undefined): string => {
    if (!size) return '-'

    // 如果已经是格式化的字符串（如 "0 B", "1.5 MB"），直接返回
    if (typeof size === 'string') {
        return size
    }

    // 处理数字类型
    const units = ['B', 'KB', 'MB', 'GB', 'TB']
    let unitIndex = 0
    let formattedSize = size

    while (formattedSize >= 1024 && unitIndex < units.length - 1) {
        formattedSize /= 1024
        unitIndex++
    }

    return `${formattedSize.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`
}

const formatDate = (date: Date): string => {
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    })
}

// 开始重命名
const startRename = (_itemId: string) => {
    // 回收站中不支持重命名
    console.log('回收站中不支持重命名')
}

// 暴露方法给父组件
defineExpose({
    startRename
})
</script>

<style scoped>
.list-view {
    width: 100%;
    height: 100%;
    overflow: auto;
}

.file-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.file-icon img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* 表格行悬停效果 */
.list-view :deep(.table-row:hover) {
    background-color: hsl(var(--muted) / 0.3);
}

/* 表格内容对齐 */
.list-view :deep(.table-cell) {
    vertical-align: middle;
}

/* 确保表格不会溢出 */
.list-view :deep(.table) {
    table-layout: fixed;
}
</style>