<template>
    <div class="flex flex-col min-h-full max-h-full">
        <!-- 主文件区域 -->
        <div ref="fileAreaRef" class="flex overflow-y-auto overflow-x-hidden relative flex-1 py-1 min-w-0 file-area"
            :class="isDetailPanelVisible ? 'pr-2 mr-2' : ''">

            <!-- 文件显示区域 -->
            <div class="flex flex-col flex-1 min-w-0">
                <div class="flex-1" v-if="displayItems.length > 0">
                    <!-- 网格视图 -->
                    <TrashGridView v-if="viewMode === 'grid'" ref="gridViewRef" :items="displayItems"
                        :show-checkboxes="true" :show-header="true" :selected-items="selectedIds"
                        :category-id="categoryId" @file-click="handleFileClick"
                        @file-double-click="handleFileDoubleClick" @folder-click="handleFolderClick"
                        @folder-double-click="handleFolderDoubleClick" @contextmenu="handleContextMenu"
                        @selection-change="handleSelectionChange" @restore="handleRestore"
                        @permanently-delete="handlePermanentlyDelete" @refresh-directory="handleRefreshDirectory" />

                    <!-- 列表视图 -->
                    <TrashListView v-else ref="listViewRef" :items="displayItems" :selected-items="selectedIds"
                        :category-id="categoryId" @file-click="handleFileClick"
                        @file-double-click="handleFileDoubleClick" @folder-click="handleFolderClick"
                        @folder-double-click="handleFolderDoubleClick" @contextmenu="handleContextMenu"
                        @selection-change="handleSelectionChange" @restore="handleRestore"
                        @permanently-delete="handlePermanentlyDelete" @refresh-directory="handleRefreshDirectory" />
                </div>

                <!-- 分页组件 -->
                <FilePagination v-model:current-page="currentPage" v-model:page-size="pageSize"
                    :total-items="totalItems" @page-change="handlePageChange"
                    @page-size-change="handlePageSizeChange" />

                <!-- 空状态 -->
                <div v-if="totalItems === 0" class="flex-1 py-12 text-center">
                    <component :is="emptyIcon" class="mx-auto mb-4 w-16 h-16 text-muted-foreground/50" />
                    <h3 class="mb-2 text-lg font-medium text-muted-foreground">{{ emptyTitle }}</h3>
                    <p class="text-sm text-muted-foreground">{{ emptyMessage }}</p>
                </div>

                <!-- 右键菜单 -->
                <TrashContextMenu :show="contextMenu.show" :x="contextMenu.x" :y="contextMenu.y"
                    :item="contextMenu.item" :selected-items="getSelectedItems()" :category-id="categoryId"
                    @close="hideContextMenu" @restore="handleContextRestore" @batch-restore="handleContextBatchRestore"
                    @permanently-delete="handleContextPermanentlyDelete"
                    @batch-permanently-delete="handleContextBatchPermanentlyDelete"
                    @refresh-directory="handleRefreshDirectory" />
            </div>

            <!-- 详细信息面板 -->
            <Transition name="detail-panel-container" enter-active-class="detail-panel-container-enter-active"
                leave-active-class="detail-panel-container-leave-active"
                enter-from-class="detail-panel-container-enter-from" leave-to-class="detail-panel-container-leave-to">
                <div v-if="isDetailPanelVisible" class="py-1 w-80 min-h-full">
                    <DetailPanel :selected-item="selectedItem"
                        :custom-properties="computedCustomProperties || undefined" :is-editing="isEditing"
                        :editing-properties="editingProperties" :is-saving="isSaving"
                        :editable-properties="editableProperties" :start-editing="startEditing"
                        :cancel-editing="cancelEditing" :save-editing="saveEditing"
                        :update-editing-property="updateEditingProperty" :format-property-value="formatPropertyValue"
                        :get-value-class="getValueClass" @close="hideDetailPanel" />
                </div>
            </Transition>
        </div>
    </div>
</template>

<script setup lang="ts">
import { reactive, computed, ref, onMounted, onUnmounted, watch } from 'vue'
import TrashGridView from '@/views/Trash/TrashGridView.vue'
import TrashListView from '@/views/Trash/TrashListView.vue'
import DetailPanel from '../Folders/DetailPanel/index.vue'
import TrashContextMenu from './TrashContextMenu.vue'
import FilePagination from '../Folders/FilesView/Pagination.vue'
import { useRectangleSelection } from '@/composables/useRectangleSelection'
import { useCustomProperties, type ApiFilterGroup } from '../Folders/DetailPanel/useCustomProperties'

import type { ItemType, FileItemType, FolderItemType } from '@/types/files'

// Props
const props = withDefaults(defineProps<{
    items: ItemType[]
    viewMode: 'grid' | 'list'
    emptyIcon?: any
    emptyTitle?: string
    emptyMessage?: string
    apiFilterOptions?: ApiFilterGroup[]
    pageSize: number
    categoryId: string
    totalItems?: number
    currentPage?: number
}>(), {
    apiFilterOptions: () => [],
    totalItems: 0,
    currentPage: 1
})

// Emits
const emit = defineEmits<{
    fileClick: [item: FileItemType]
    fileDoubleClick: [item: FileItemType]
    folderClick: [item: FolderItemType]
    folderDoubleClick: [item: FolderItemType]
    itemRenamed: [itemId: string, newName: string, item: ItemType]
    restoreSuccess: [items: ItemType[]]
    deleteSuccess: [items: ItemType[]]
    refreshDirectory: []
    'page-change': [page: number]
    'page-size-change': [size: number]
}>()

// 分页状态 - 使用props传递的值
const currentPage = computed(() => props.currentPage)
const pageSize = computed(() => props.pageSize)

// 详细信息面板状态
const isDetailPanelVisible = ref(false)
const selectedItem = ref<ItemType | null>(null)

// 多选状态
const selectedIds = ref<Set<string>>(new Set())

// 主文件区域引用
const fileAreaRef = ref<HTMLElement | null>(null)
const gridViewRef = ref<InstanceType<typeof TrashGridView> | null>(null)
const listViewRef = ref<InstanceType<typeof TrashListView> | null>(null)

// 右键菜单
const contextMenu = reactive({
    show: false,
    x: 0,
    y: 0,
    item: null as ItemType | null
})

// 计算属性
const displayItems = computed(() => props.items)

// 分页相关计算属性 - 后端分页，参考getDirectoryContents
const totalItems = computed(() => props.totalItems || 0)

// 分页事件处理 - 后端分页，参考getDirectoryContents
const handlePageChange = (page: number) => {
    selectedIds.value.clear()
    emit('page-change', page)
}

const handlePageSizeChange = (size: number) => {
    // 清空选中状态
    selectedIds.value.clear()
    emit('page-size-change', size)
}

// 监听数据变化，重置分页
watch(() => props.items, () => {
    // 当数据源变化时，清空选中状态
    selectedIds.value.clear()
}, { flush: 'sync' })

// 详细信息面板控制
const showDetailPanelFn = (item: ItemType) => {
    selectedItem.value = {
        ...item,
        createdAt: new Date(item.modifiedAt.getTime() - 24 * 60 * 60 * 1000), // 模拟创建时间
        path: item.type === 'folder' ? (item as any).path : undefined
    }
    isDetailPanelVisible.value = true
}

const hideDetailPanel = () => {
    isDetailPanelVisible.value = false
    selectedItem.value = null
}

// 使用自定义属性 composable
const {
    customProperties: computedCustomProperties,
    isEditing,
    editingProperties,
    isSaving,
    editableProperties,
    startEditing,
    cancelEditing,
    saveEditing,
    updateEditingProperty,
    formatPropertyValue,
    getValueClass
} = useCustomProperties(
    computed(() => selectedItem.value),
    computed(() => props.apiFilterOptions)
)

// 路由信息现在由父组件处理，不再需要在这里获取

// 事件处理
const handleFileClick = (item: FileItemType) => {
    console.log('打开文件:', item.name)
    showDetailPanelFn(item)
    emit('fileClick', item)
}

const handleFileDoubleClick = (item: FileItemType) => {
    console.log('双击文件:', item.name)
    emit('fileDoubleClick', item)
}

const handleFolderClick = (item: FolderItemType) => {
    console.log('选择文件夹:', item.name)
    // 单击文件夹只显示详情，回收站中不能进入文件夹
    showDetailPanelFn(item)
}

const handleFolderDoubleClick = (item: FolderItemType) => {
    console.log('双击文件夹:', item.name)
    // 回收站中不能进入文件夹，触发父组件的处理
    emit('folderDoubleClick', item)
}

const handleContextMenu = (event: MouseEvent, item: ItemType) => {
    contextMenu.show = true
    contextMenu.x = event.clientX
    contextMenu.y = event.clientY
    contextMenu.item = item
}

// 框选功能
const { setSelectedItems } = useRectangleSelection({
    containerRef: fileAreaRef,
    itemSelector: '[data-id]',
    getItemId: (element) => {
        return element.dataset.id || null
    },
    onSelectionChange: (newSelectedIds) => {
        selectedIds.value = newSelectedIds
    }
})

const handleSelectionChange = (newSelectedIds: Set<string>) => {
    selectedIds.value = newSelectedIds
    setSelectedItems(newSelectedIds)
}

// 处理还原操作
const handleRestore = () => {
    if (selectedIds.value.size === 0) return

    const selectedItems = displayItems.value.filter(item => selectedIds.value.has(item.id))
    emit('restoreSuccess', selectedItems)

    // 清除选择
    selectedIds.value.clear()
}

// 处理彻底删除操作
const handlePermanentlyDelete = () => {
    if (selectedIds.value.size === 0) return

    const selectedItems = displayItems.value.filter(item => selectedIds.value.has(item.id))
    emit('deleteSuccess', selectedItems)

    // 清除选择
    selectedIds.value.clear()
}

// 处理上下文菜单还原
const handleContextRestore = (item: ItemType) => {
    emit('restoreSuccess', [item])
}

// 处理上下文菜单批量还原
const handleContextBatchRestore = (items: ItemType[]) => {
    emit('restoreSuccess', items)
}

// 处理上下文菜单彻底删除
const handleContextPermanentlyDelete = (item: ItemType) => {
    emit('deleteSuccess', [item])
}

// 处理上下文菜单批量彻底删除
const handleContextBatchPermanentlyDelete = (items: ItemType[]) => {
    emit('deleteSuccess', items)
}

// 获取当前选中的文件列表
const getSelectedItems = (): ItemType[] => {
    return displayItems.value.filter(item => selectedIds.value.has(item.id))
}

const hideContextMenu = () => {
    contextMenu.show = false
    contextMenu.item = null
}

const handleRefreshDirectory = () => {
    emit('refreshDirectory')
}

// 生命周期
onMounted(() => {
    document.addEventListener('click', hideContextMenu)
})

onUnmounted(() => {
    document.removeEventListener('click', hideContextMenu)
})

// 暴露一些方法给父组件
defineExpose({
    // 分页控制
    getCurrentPage: () => currentPage.value,
    getPageSize: () => pageSize.value,
    // 选中状态控制
    clearSelection: () => {
        selectedIds.value.clear()
    },
    getSelectedItems: () => getSelectedItems(),
    // 详细信息面板控制
    showDetailPanel: showDetailPanelFn,
    hideDetailPanel,
})
</script>

<style scoped>
/* 详细信息面板容器动画 */
.detail-panel-container-enter-active,
.detail-panel-container-leave-active {
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.detail-panel-container-enter-from {
    width: 0;
    opacity: 0;
    transform: translateX(50px);
}

.detail-panel-container-leave-to {
    width: 0;
    opacity: 0;
    transform: translateX(50px);
}

/* 优化动画性能 */
.detail-panel-container-enter-active,
.detail-panel-container-leave-active {
    will-change: width, opacity, transform;
    overflow: hidden;
}

/* 文件区域框选样式 */
.file-area {
    position: relative;
    user-select: none;
}

/* 框选时禁用所有过渡效果，避免干扰 */
.file-area:has(.rectangle-selection) * {
    transition: none !important;
}
</style>