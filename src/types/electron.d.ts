// Electron API 类型定义

export interface TusUploadConfig {
  endpoint: string;
  chunkSize?: number;
  retryDelays?: number[];
  parallelUploads?: number;
  metadata?: Record<string, string>;
  headers?: Record<string, string>;
}

export interface UploadTask {
  id: string;
  filePath: string;
  fileName: string;
  fileSize: number;
  uploadUrl?: string;
  progress: number;
  status: "pending" | "uploading" | "paused" | "completed" | "error" | "cancelled";
  bytesUploaded: number;
  uploadSpeed: number;
  remainingTime: number;
  startTime: Date;
  error?: string;
  metadata?: Record<string, string>;
  resumable: boolean;
  batchId?: string;
  isSubTask?: boolean;
}

export interface ApiResponse<T = any> {
  success: boolean;
  error?: string;
  taskId?: string;
  task?: UploadTask;
  tasks?: UploadTask[];
  data?: T;
}

// 批量上传任务类型
export interface BatchUploadTask {
  id: string;
  type: "batch";
  batchName: string;
  folderPath?: string;
  totalFiles: number;
  completedFiles: number;
  failedFiles: number;
  pausedFiles: number;
  totalSize: number;
  uploadedSize: number;
  progress: number;
  status: "pending" | "uploading" | "paused" | "completed" | "error" | "cancelled";
  startTime: Date;
  endTime?: Date;
  error?: string;
  subTasks: string[];
  expanded?: boolean;
  metadata?: Record<string, string>;
  avgUploadSpeed: number;
  estimatedRemainingTime: number;
}

// 下载相关类型
export interface DownloadTask {
  id: string;
  url: string;
  fileName: string;
  filePath: string;
  fileSize?: number;
  progress: number;
  status: "pending" | "downloading" | "paused" | "completed" | "error" | "cancelled";
  bytesDownloaded: number;
  downloadSpeed: number;
  remainingTime: number;
  startTime: Date;
  endTime?: Date;
  error?: string;
  metadata?: Record<string, any>;
  resumable: boolean;
  chunkSize: number;
  totalChunks: number;
  completedChunks: number;
  headers?: Record<string, string>;
}

export interface StreamDownloadConfig {
  chunkSize?: number;
  maxConcurrent?: number;
  retryDelays?: number[];
  timeout?: number;
  headers?: Record<string, string>;
  downloadDir?: string;
}

export interface DownloadApiResponse<T = any> {
  success: boolean;
  error?: string;
  taskId?: string;
  task?: DownloadTask;
  tasks?: DownloadTask[];
  data?: T;
}

// 解压缩相关类型
export interface ExtractionTask {
  id: string;
  archivePath: string;
  extractPath: string;
  fileName: string;
  fileSize?: number;
  extractedSize: number;
  totalSize?: number;
  progress: number;
  status: "pending" | "extracting" | "paused" | "completed" | "error" | "cancelled";
  startTime: Date;
  endTime?: Date;
  error?: string;
  speed: number;
  remainingTime: number;
  extractedFiles: number;
  totalFiles?: number;
  currentFile?: string;
  metadata?: Record<string, any>;
  downloadTaskId?: string;
  requiresPassword?: boolean;
  password?: string;
  deleteAfterExtraction?: boolean;
}

export interface ExtractionConfig {
  maxConcurrent?: number;
  timeout?: number;
  deleteOriginalAfterExtraction?: boolean;
  overwriteExisting?: boolean;
  createSubfolder?: boolean;
  passwordPromptTimeout?: number;
}

export interface ExtractionApiResponse<T = any> {
  success: boolean;
  error?: string;
  taskId?: string;
  task?: ExtractionTask;
  tasks?: ExtractionTask[];
  data?: T;
}

// 自动更新相关类型
export interface VersionInfo {
  version: string;
  releaseNotes: string;
  releaseDate: string;
  downloadUrl: string;
  fileSize: number;
  checksum?: string;
}

export interface UpdateCheckResult {
  hasUpdate: boolean;
  currentVersion: string;
  latestVersion?: VersionInfo;
}

export interface DownloadProgress {
  percent: number;
  bytesDownloaded: number;
  totalBytes: number;
  speed: number;
}

export interface UpdateStatus {
  status: 'checking' | 'available' | 'downloading' | 'downloaded' | 'installing' | 'installed' | 'error' | 'not-available';
  progress?: DownloadProgress;
  error?: string;
  version?: VersionInfo;
}

declare global {
  interface Window {
    electronAPI: {
      // 基础功能
      getAppVersion: () => Promise<string>;
      getPlatform: () => Promise<string>;
      showOpenDialog: (options: any) => Promise<any>;
      showSaveDialog: (options: any) => Promise<any>;
      showMessageBox: (options: any) => Promise<any>;

      // 文件系统 API
      getFileInfo: (filePath: string) => Promise<ApiResponse>;

      // 拖拽文件处理 API
      dragDrop: {
        handleFileDrop: (filePaths: string[]) => Promise<ApiResponse>;
      };

      // 文件夹选择 API
      folderSelect: {
        selectFolderAndGetFiles: () => Promise<ApiResponse>;
      };

      // TUS 上传功能（整合了 API 调用和事件监听器）
      tus: {
        // API 调用方法
        createUpload: (filePath: string, metadata?: Record<string, string>) => Promise<ApiResponse>;
        createUploadFromDialog: () => Promise<ApiResponse>;
        startUpload: (taskId: string) => Promise<ApiResponse>;
        pauseUpload: (taskId: string) => Promise<ApiResponse>;
        resumeUpload: (taskId: string) => Promise<ApiResponse>;
        cancelUpload: (taskId: string) => Promise<ApiResponse>;
        retryUpload: (taskId: string) => Promise<ApiResponse>;
        deleteTask: (taskId: string) => Promise<ApiResponse>;
        getAllTasks: () => Promise<ApiResponse>;
        getTask: (taskId: string) => Promise<ApiResponse>;
        getActiveTasks: () => Promise<ApiResponse>;
        getTaskUploadUrl: (taskId: string) => Promise<ApiResponse>;
        updateConfig: (config: Partial<TusUploadConfig>) => Promise<ApiResponse>;
        clearCompletedTasks: () => Promise<ApiResponse>;

        // 智能打包相关方法
        smartPackUpload: (
          filePaths: string[],
          options?: {
            threshold?: number;
            archiveName?: string;
            metadata?: Record<string, string>;
            skipConfirmation?: boolean;
          }
        ) => Promise<ApiResponse>;
        analyzeSmartPacking: (
          filePaths: string[],
          options?: {
            threshold?: number;
            maxSize?: number;
          }
        ) => Promise<ApiResponse>;
        createUploadsFromPaths: (filePaths: string[], metadata?: Record<string, string>) => Promise<ApiResponse>;
        createUploadFromFile: (fileData: { name: string; content: ArrayBuffer; metadata?: Record<string, string> }) => Promise<ApiResponse>;
        getFileInfo: (filePath: string) => Promise<ApiResponse>;
        selectFolderForPacking: () => Promise<ApiResponse>;

        // 事件监听器方法
        onUploadTaskCreated: (callback: (taskId: string, task: UploadTask) => void) => void;
        onUploadTaskProgress: (callback: (taskId: string, progress: number, bytesUploaded: number, bytesTotal: number) => void) => void;
        onUploadTaskStatusChanged: (callback: (taskId: string, status: string, error?: string) => void) => void;
        onUploadTaskCompleted: (callback: (taskId: string) => void) => void;
        onUploadTaskError: (callback: (taskId: string, error: string) => void) => void;
        removeAllListeners: (channel: string) => void;
      };

      // StreamSaver 下载功能（整合了 API 调用和事件监听器）
      download: {
        // API 调用方法
        createTask: (fileName?: string, savePath?: string, metadata?: Record<string, string>) => Promise<DownloadApiResponse>;
        createTasks: (downloads: Array<{ fileName?: string; savePath?: string; metadata?: Record<string, string> }>) => Promise<DownloadApiResponse>;
        createTaskWithDialog: (defaultFileName?: string, metadata?: Record<string, string>) => Promise<DownloadApiResponse>;
        startDownload: (taskId: string) => Promise<DownloadApiResponse>;
        pauseDownload: (taskId: string) => Promise<DownloadApiResponse>;
        resumeDownload: (taskId: string) => Promise<DownloadApiResponse>;
        cancelDownload: (taskId: string) => Promise<DownloadApiResponse>;
        retryDownload: (taskId: string) => Promise<DownloadApiResponse>;
        deleteTask: (taskId: string) => Promise<DownloadApiResponse>;
        getAllTasks: () => Promise<DownloadApiResponse>;
        getTask: (taskId: string) => Promise<DownloadApiResponse>;
        getActiveTasks: () => Promise<DownloadApiResponse>;
        updateConfig: (config: Partial<StreamDownloadConfig>) => Promise<DownloadApiResponse>;
        clearCompletedTasks: () => Promise<DownloadApiResponse>;
        clearAllTasks: () => Promise<DownloadApiResponse>;
        startBatch: (taskIds: string[]) => Promise<DownloadApiResponse>;
        pauseBatch: (taskIds: string[]) => Promise<DownloadApiResponse>;
        resumeBatch: (taskIds: string[]) => Promise<DownloadApiResponse>;
        getStats: () => Promise<DownloadApiResponse>;

        // 事件监听器方法
        onDownloadTaskCreated: (callback: (taskId: string, task: DownloadTask) => void) => void;
        onDownloadTaskProgress: (callback: (taskId: string, progress: number, bytesDownloaded: number, bytesTotal: number) => void) => void;
        onDownloadTaskStatusChanged: (callback: (taskId: string, status: string, error?: string) => void) => void;
        onDownloadTaskCompleted: (callback: (taskId: string) => void) => void;
        onDownloadTaskError: (callback: (taskId: string, error: string) => void) => void;
        removeAllListeners: (channel: string) => void;
      };

      // 7z 解压缩功能（整合了 API 调用和事件监听器）
      extraction: {
        // API 调用方法
        createTask: (
          archivePath: string,
          extractPath?: string,
          options?: {
            downloadTaskId?: string;
            deleteAfterExtraction?: boolean;
            password?: string;
          }
        ) => Promise<ExtractionApiResponse>;
        createTaskWithDialog: (
          archivePath: string,
          options?: {
            downloadTaskId?: string;
            deleteAfterExtraction?: boolean;
            password?: string;
          }
        ) => Promise<ExtractionApiResponse>;
        startExtraction: (taskId: string) => Promise<ExtractionApiResponse>;
        pauseExtraction: (taskId: string) => Promise<ExtractionApiResponse>;
        resumeExtraction: (taskId: string) => Promise<ExtractionApiResponse>;
        cancelExtraction: (taskId: string) => Promise<ExtractionApiResponse>;
        deleteTask: (taskId: string) => Promise<ExtractionApiResponse>;
        getAllTasks: () => Promise<ExtractionApiResponse>;
        getTask: (taskId: string) => Promise<ExtractionApiResponse>;
        getActiveTasks: () => Promise<ExtractionApiResponse>;
        clearCompletedTasks: () => Promise<ExtractionApiResponse>;
        clearAllTasks: () => Promise<ExtractionApiResponse>;
        startBatch: (taskIds: string[]) => Promise<ExtractionApiResponse>;
        pauseBatch: (taskIds: string[]) => Promise<ExtractionApiResponse>;
        resumeBatch: (taskIds: string[]) => Promise<ExtractionApiResponse>;
        cancelBatch: (taskIds: string[]) => Promise<ExtractionApiResponse>;
        providePassword: (taskId: string, password: string) => Promise<ExtractionApiResponse>;
        handleFileConflict: (taskId: string, action: "overwrite" | "skip" | "rename") => Promise<ExtractionApiResponse>;

        // 事件监听器方法
        onExtractionTaskCreated: (callback: (taskId: string, task: ExtractionTask) => void) => void;
        onExtractionTaskProgress: (callback: (taskId: string, progress: number, extractedSize: number, totalSize?: number) => void) => void;
        onExtractionTaskStatusChanged: (callback: (taskId: string, status: string, error?: string) => void) => void;
        onExtractionTaskCompleted: (callback: (taskId: string, extractPath: string) => void) => void;
        onExtractionTaskError: (callback: (taskId: string, error: string) => void) => void;
        onExtractionPasswordRequired: (callback: (taskId: string, callback: (password?: string) => void) => void) => void;
        onExtractionFileConflict: (callback: (taskId: string, filePath: string, callback: (action: "overwrite" | "skip" | "rename") => void) => void) => void;
        removeAllListeners: (channel: string) => void;
      };

      // 主进程消息监听器
      onMainProcessMessage: (callback: (data: string) => void) => void;

      // 模块就绪通知
      onModulesReady: (callback: () => void) => void;

      // Shell API
      shell: {
        openPath: (path: string) => Promise<string>;
        showItemInFolder: (fullPath: string) => void;
      };

      // 自动更新 API
      autoUpdater: {
        // API 调用方法
        checkForUpdates: () => Promise<UpdateCheckResult>;
        downloadUpdate: (versionInfo: VersionInfo) => Promise<boolean>;
        installUpdate: () => Promise<boolean>;
        getStatus: () => Promise<UpdateStatus>;
        setUpdateUrl: (url: string) => Promise<void>;
        cleanupDownloads: () => Promise<void>;

        // 事件监听器方法
        onUpdateCheckStart: (callback: () => void) => void;
        onUpdateCheckResult: (callback: (result: UpdateCheckResult) => void) => void;
        onUpdateAvailable: (callback: (versionInfo: VersionInfo) => void) => void;
        onUpdateNotAvailable: (callback: () => void) => void;
        onDownloadStart: (callback: (versionInfo: VersionInfo) => void) => void;
        onDownloadProgress: (callback: (progress: DownloadProgress) => void) => void;
        onDownloadComplete: (callback: (filePath: string) => void) => void;
        onDownloadError: (callback: (error: string) => void) => void;
        onInstallStart: (callback: () => void) => void;
        onInstallComplete: (callback: () => void) => void;
        onInstallError: (callback: (error: string) => void) => void;
        onInstallCancelled: (callback: () => void) => void;
        onShowUpdateNotification: (callback: (versionInfo: VersionInfo) => void) => void;
        removeAllListeners: () => void;
      };

      // 认证相关 API
      onAuthToken: (callback: (token: string) => void) => void;
      clearAuthState: () => void;
    };
  }
}
