<template>
    <Dialog v-model:open="isOpen">
        <DialogContent class="sm:max-w-[400px] p-0 gap-0" @pointer-down-outside="$event.preventDefault()"
            @interact-outside="$event.preventDefault()" @escape-key-down="$event.preventDefault()">
            <!-- 关闭按钮 -->
            <div class="absolute right-4 top-4 z-10">
                <button @click="handleClose" class="text-gray-400 hover:text-gray-600 transition-colors">
                </button>
            </div>

            <!-- 内容区域 -->
            <div class="flex flex-col items-center text-center p-8">
                <!-- 图标 -->
                <div class="w-16 h-16 rounded-full flex items-center justify-center mb-4">
                    <img src="./icon1.png" alt="星图图标" class="w-16 h-16 rounded-full" />
                </div>

                <!-- 标题 -->
                <h2 class="text-lg font-medium text-gray-900 mb-2">星图</h2>
                <div class="text-sm text-gray-600 mb-6 space-y-1">
                    <p>新版本下载完成</p>
                    <div v-if="versionInfo" class="space-y-1">
                        <p><span class="font-medium">版本：</span>{{ versionInfo.version }}</p>
                        <!-- <p class="text-xs text-gray-500">{{ versionInfo.releaseDate }}</p> -->
                    </div>
                </div>

                <!-- 发行说明（如果有的话） -->
                <div v-if="versionInfo?.releaseNotes"
                    class="w-full p-3 bg-blue-50 border border-blue-200 rounded-md mb-6">
                    <h4 class="text-sm font-medium text-blue-800 mb-2">更新内容</h4>
                    <p class="text-sm text-blue-700 whitespace-pre-line">{{ versionInfo.releaseNotes }}</p>
                </div>

                <!-- 安装提示 -->
                <div class="w-full p-3 bg-yellow-50 border border-yellow-200 rounded-md mb-6">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" fill="currentColor"
                            viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                clip-rule="evenodd" />
                        </svg>
                        <div>
                            <p class="text-sm text-yellow-800">安装更新将关闭应用程序</p>
                            <p class="text-xs text-yellow-700 mt-1">请确保已保存所有工作</p>
                        </div>
                    </div>
                </div>

                <!-- 按钮组 -->
                <div class="flex gap-3 w-full">
                    <button @click="handleLater"
                        class="flex-1 px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                        稍后安装
                    </button>
                    <button @click="handleInstallNow" :disabled="isInstalling"
                        class="flex-1 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-blue-300 transition-colors flex items-center justify-center">
                        <svg v-if="isInstalling" class="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                            </circle>
                            <path class="opacity-75" fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                            </path>
                        </svg>
                        {{ isInstalling ? '正在安装...' : '立即安装' }}
                    </button>
                </div>
            </div>
        </DialogContent>
    </Dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import type { VersionInfo } from '../../types/electron'

interface Props {
    open?: boolean
    versionInfo?: VersionInfo
    isInstalling?: boolean
}

interface Emits {
    'update:open': [value: boolean]
    'install-now': []
    'install-later': []
    'close': []
}

const props = withDefaults(defineProps<Props>(), {
    open: false,
    isInstalling: false
})

const emit = defineEmits<Emits>()

const isOpen = computed({
    get: () => props.open,
    set: (value: boolean) => emit('update:open', value)
})

const handleInstallNow = () => {
    emit('install-now')
}

const handleLater = () => {
    emit('install-later')
    isOpen.value = false
}

const handleClose = () => {
    emit('close')
    isOpen.value = false
}
</script>