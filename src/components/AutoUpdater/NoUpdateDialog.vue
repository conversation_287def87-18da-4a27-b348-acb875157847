<template>
    <Dialog v-model:open="isOpen">
        <DialogContent class="sm:max-w-[400px] p-0 gap-0" @pointer-down-outside="$event.preventDefault()"
            @interact-outside="$event.preventDefault()" @escape-key-down="$event.preventDefault()">
            <!-- 关闭按钮 -->
            <div class="absolute right-4 top-4 z-10">
                <button @click="handleClose" class="text-gray-400 hover:text-gray-600 transition-colors">
                </button>
            </div>

            <!-- 内容区域 -->
            <div class="flex flex-col items-center text-center p-8">
                <!-- 图标 -->
                <div class="w-16 h-16 rounded-full flex items-center justify-center mb-4">
                    <img src="./icon1.png" alt="星图图标" class="w-16 h-16 rounded-full" />
                </div>

                <!-- 标题 -->
                <h2 class="text-lg font-medium text-gray-900 mb-2">星图</h2>
                <div class="text-sm text-gray-600 mb-6 space-y-1">
                    <p>{{ message }}</p>
                    <div v-if="currentVersion" class="space-y-1">
                        <p><span class="font-medium">当前版本：</span>{{ currentVersion }}</p>
                        <!-- <p class="text-xs text-gray-500">{{ checkTime }}</p> -->
                    </div>
                </div>

                <!-- 按钮 -->
                <div class="w-full">
                    <button @click="handleClose"
                        class="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
                        确定
                    </button>
                </div>
            </div>
        </DialogContent>
    </Dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Dialog, DialogContent } from '@/components/ui/dialog'

interface Props {
    open?: boolean
    currentVersion?: string
    message?: string
    isError?: boolean
}

interface Emits {
    'update:open': [value: boolean]
    'close': []
}

const props = withDefaults(defineProps<Props>(), {
    open: false,
    currentVersion: '1.0.0',
    message: '已是最新版本',
    isError: false
})

const emit = defineEmits<Emits>()

const isOpen = computed({
    get: () => props.open,
    set: (value: boolean) => emit('update:open', value)
})

// const checkTime = computed(() => {
//     return new Date().toLocaleString('zh-CN')
// })

const handleClose = () => {
    emit('close')
    isOpen.value = false
}
</script>