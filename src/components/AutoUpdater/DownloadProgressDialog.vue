<template>
    <Dialog v-model:open="isOpen">
        <DialogContent class="sm:max-w-[400px] p-0 gap-0" @pointer-down-outside="$event.preventDefault()"
            @interact-outside="$event.preventDefault()" @escape-key-down="$event.preventDefault()">
            <!-- 关闭按钮（可选是否显示） -->
            <div v-if="showCloseButton" class="absolute right-4 top-4 z-10">
                <button @click="handleClose" class="text-gray-400 hover:text-gray-600 transition-colors">
                </button>
            </div>

            <!-- 内容区域 -->
            <div class="flex flex-col items-center text-center p-8">
                <!-- 图标 -->
                <div class="w-16 h-16 rounded-full flex items-center justify-center mb-4">
                    <img src="./icon1.png" alt="星图图标" class="w-16 h-16 rounded-full"
                        :class="{ 'animate-pulse': isDownloading }" />
                </div>

                <!-- 标题 -->
                <h2 class="text-lg font-medium text-gray-900 mb-2">星图</h2>
                <p class="text-sm text-gray-600 mb-6">
                    {{ statusText }}
                </p>

                <!-- 进度条 -->
                <div v-if="showProgress" class="w-full mb-4">
                    <div class="flex justify-between text-xs text-gray-600 mb-2">
                        <span>{{ progress.percent }}%</span>
                        <span>{{ downloadSpeed }}</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out"
                            :style="{ width: `${progress.percent}%` }"></div>
                    </div>
                    <div class="text-xs text-gray-500 mt-2">
                        {{ downloadSize }}
                    </div>
                </div>

                <!-- 错误信息 -->
                <div v-if="error" class="w-full p-3 bg-red-50 border border-red-200 rounded-md mb-4">
                    <p class="text-sm text-red-600">{{ error }}</p>
                </div>

                <!-- 按钮组 -->
                <div class="flex gap-3 w-full">
                    <button v-if="showRetryButton" @click="handleRetry"
                        class="flex-1 px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                        重试
                    </button>
                    <button v-if="showCancelButton" @click="handleCancel"
                        class="flex-1 px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                        取消
                    </button>
                </div>
            </div>
        </DialogContent>
    </Dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import type { DownloadProgress } from '../../types/electron'

interface Props {
    open?: boolean
    progress?: DownloadProgress
    isDownloading?: boolean
    error?: string
    showCloseButton?: boolean
    showRetryButton?: boolean
    showCancelButton?: boolean
}

interface Emits {
    'update:open': [value: boolean]
    'retry': []
    'cancel': []
    'close': []
}

const props = withDefaults(defineProps<Props>(), {
    open: false,
    isDownloading: false,
    showCloseButton: false,
    showRetryButton: false,
    showCancelButton: true,
    progress: () => ({
        percent: 0,
        bytesDownloaded: 0,
        totalBytes: 0,
        speed: 0
    })
})

const emit = defineEmits<Emits>()

const isOpen = computed({
    get: () => props.open,
    set: (value: boolean) => emit('update:open', value)
})

const statusText = computed(() => {
    if (props.error) {
        return '下载失败'
    }
    if (props.isDownloading) {
        return '正在下载新版本...'
    }
    return '准备下载'
})

const showProgress = computed(() => {
    return props.isDownloading && props.progress && props.progress.totalBytes > 0
})

const downloadSpeed = computed(() => {
    if (!props.progress || props.progress.speed === 0) {
        return ''
    }
    const speed = props.progress.speed
    if (speed < 1024 * 1024) {
        return `${(speed / 1024).toFixed(1)} KB/s`
    }
    return `${(speed / (1024 * 1024)).toFixed(1)} MB/s`
})

const downloadSize = computed(() => {
    if (!props.progress || props.progress.totalBytes === 0) {
        return ''
    }
    const downloaded = props.progress.bytesDownloaded
    const total = props.progress.totalBytes

    const formatBytes = (bytes: number) => {
        if (bytes < 1024 * 1024) {
            return `${(bytes / 1024).toFixed(1)} KB`
        }
        return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
    }

    return `${formatBytes(downloaded)} / ${formatBytes(total)}`
})

const handleRetry = () => {
    emit('retry')
}

const handleCancel = () => {
    emit('cancel')
    isOpen.value = false
}

const handleClose = () => {
    emit('close')
    isOpen.value = false
}
</script>