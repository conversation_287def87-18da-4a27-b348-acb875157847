export interface VersionInfo {
  version: string
  releaseNotes: string
  releaseDate: string
  downloadUrl: string
  fileSize: number
  checksum?: string
}

export interface UpdateCheckResult {
  hasUpdate: boolean
  currentVersion: string
  latestVersion?: VersionInfo
  error?: string
}

export interface DownloadProgress {
  percent: number
  bytesDownloaded: number
  totalBytes: number
  speed: number
}

export interface UpdateStatus {
  status: 'checking' | 'available' | 'downloading' | 'downloaded' | 'installing' | 'installed' | 'error' | 'not-available'
  progress?: DownloadProgress
  error?: string
  version?: VersionInfo
}

export interface UpdateEvents {
  'update-check-start': void
  'update-check-result': UpdateCheckResult
  'update-available': VersionInfo
  'update-not-available': void
  'download-start': VersionInfo
  'download-progress': DownloadProgress
  'download-complete': string // file path
  'download-error': string
  'install-start': void
  'install-complete': void
  'install-error': string
} 