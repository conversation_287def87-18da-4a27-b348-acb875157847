import { ipc<PERSON><PERSON>, BrowserWindow } from 'electron'
import { getUpdateManager } from './updateManager'
import { createLogger } from '../logger'
import type { UpdateCheckResult, VersionInfo, UpdateStatus } from './types'

const logger = createLogger('AutoUpdater-IPC')

let mainWindow: BrowserWindow | null = null

export function setupAutoUpdaterIpcHandlers(window: BrowserWindow) {
  mainWindow = window
  const updateManager = getUpdateManager()

  // 监听更新管理器事件，转发给渲染进程
  setupEventForwarding(updateManager, window)

  // 检查更新
  ipcMain.handle('auto-updater:check-for-updates', async (): Promise<UpdateCheckResult> => {
    try {
      logger.info('Checking for updates via IPC (manual check)')
      return await updateManager.checkForUpdates(true) // 标记为手动检测
    } catch (error) {
      logger.error('Failed to check for updates:', error)
      return {
        hasUpdate: false,
        currentVersion: updateManager.getStatus().version?.version || '0.0.0'
      }
    }
  })

  // 开始下载更新
  ipcMain.handle('auto-updater:download-update', async (_, versionInfo: VersionInfo): Promise<boolean> => {
    try {
      logger.info('Starting download via IPC:', versionInfo.version)
      await updateManager.downloadUpdate(versionInfo)
      return true
    } catch (error) {
      logger.error('Failed to download update:', error)
      return false
    }
  })

  // 安装更新
  ipcMain.handle('auto-updater:install-update', async (): Promise<boolean> => {
    try {
      logger.info('Installing update via IPC')
      await updateManager.installUpdate()
      return true
    } catch (error) {
      logger.error('Failed to install update:', error)
      return false
    }
  })

  // 获取更新状态
  ipcMain.handle('auto-updater:get-status', (): UpdateStatus => {
    return updateManager.getStatus()
  })

  // 设置更新服务器地址（使用官方electron-updater，URL在构造函数中设置）
  ipcMain.handle('auto-updater:set-update-url', (_, url: string): void => {
    logger.info('Update URL is configured in UpdateManager constructor:', url)
    // 官方electron-updater的URL在构造函数中设置，这里保持接口兼容性
  })

  // 清理下载文件
  ipcMain.handle('auto-updater:cleanup-downloads', (): void => {
    logger.info('Cleaning up downloads via IPC')
    updateManager.cleanupDownloads()
  })

  // 取消下载（目前不支持，但预留接口）
  ipcMain.handle('auto-updater:cancel-download', (): boolean => {
    logger.warn('Cancel download not implemented yet')
    return false
  })
}

// 设置事件转发
function setupEventForwarding(updateManager: any, window: BrowserWindow) {
  // 检查更新开始
  updateManager.on('update-check-start', () => {
    window.webContents.send('auto-updater:update-check-start')
  })

  // 检查更新结果
  updateManager.on('update-check-result', (result: UpdateCheckResult) => {
    window.webContents.send('auto-updater:update-check-result', result)
  })

  // 发现更新
  updateManager.on('update-available', (versionInfo: VersionInfo) => {
    window.webContents.send('auto-updater:update-available', versionInfo)
  })

  // 没有更新
  updateManager.on('update-not-available', () => {
    window.webContents.send('auto-updater:update-not-available')
  })

  // 开始下载
  updateManager.on('download-start', (versionInfo: VersionInfo) => {
    window.webContents.send('auto-updater:download-start', versionInfo)
  })

  // 下载进度
  updateManager.on('download-progress', (progress: any) => {
    window.webContents.send('auto-updater:download-progress', progress)
  })

  // 下载完成
  updateManager.on('download-complete', (filePath: string) => {
    window.webContents.send('auto-updater:download-complete', filePath)
  })

  // 下载错误
  updateManager.on('download-error', (error: string) => {
    window.webContents.send('auto-updater:download-error', error)
  })

  // 开始安装
  updateManager.on('install-start', () => {
    window.webContents.send('auto-updater:install-start')
  })

  // 安装完成
  updateManager.on('install-complete', () => {
    window.webContents.send('auto-updater:install-complete')
  })

  // 安装错误
  updateManager.on('install-error', (error: string) => {
    window.webContents.send('auto-updater:install-error', error)
  })

  // 安装取消
  updateManager.on('install-cancelled', () => {
    window.webContents.send('auto-updater:install-cancelled')
  })
}

// 自动检查更新（启动时调用）
export async function performAutoUpdate(): Promise<void> {
  if (!mainWindow) {
    logger.warn('Main window not available for auto update')
    return
  }

  try {
    const updateManager = getUpdateManager()
    logger.info('Performing automatic update check on startup')
    
    const result = await updateManager.checkForUpdates(false) // 标记为自动检测
    
    if (result.hasUpdate && result.latestVersion) {
      logger.info('Update available, showing notification to user')
      // 发送事件到渲染进程，显示更新提示
      mainWindow.webContents.send('auto-updater:show-update-notification', result.latestVersion)
    } else {
      logger.info('No update available, silent handling')
      // 没有更新时静默处理，不发送任何事件
    }
  } catch (error) {
    logger.error('Auto update check failed:', error)
  }
}

export function cleanupAutoUpdaterIpcHandlers() {
  // 清理IPC处理器
  ipcMain.removeHandler('auto-updater:check-for-updates')
  ipcMain.removeHandler('auto-updater:download-update')
  ipcMain.removeHandler('auto-updater:install-update')
  ipcMain.removeHandler('auto-updater:get-status')
  ipcMain.removeHandler('auto-updater:set-update-url')
  ipcMain.removeHandler('auto-updater:cleanup-downloads')
  ipcMain.removeHandler('auto-updater:cancel-download')
  
  mainWindow = null
  logger.info('Auto updater IPC handlers cleaned up')
} 