import { contextBridge, ipcRenderer } from "electron";
import { createSimplifiedTusPreloadApi } from "./tus/preloadApi";
import { createSimplifiedDownloadPreloadApi } from "./stream-downloader/preloadApi";
import { createSimplifiedArchivePreloadApi } from "./archive/preloadApi";
import { createSimplifiedExtractionPreloadApi } from "./7z-extractor/preloadApi";

// 从 TUS 模块导出类型，保持向后兼容性
export type { UploadTask, TusUploadConfig } from "./tus/types";

// 从下载模块导出类型
export type { DownloadTask, StreamDownloadConfig } from "./stream-downloader/types";

// 从压缩模块导出类型
export type { ArchiveTask, ArchiveConfig } from "./archive/types";

// 从解压缩模块导出类型
export type { ExtractionTask, ExtractionConfig } from "./7z-extractor/types";

// 从自动更新模块导入和导出类型
import type { VersionInfo } from "./auto-updater/types";
export type { 
  VersionInfo, 
  UpdateCheckResult, 
  DownloadProgress, 
  UpdateStatus 
} from "./auto-updater/types";

// 创建简化的 TUS API（整合了 API 调用和事件监听器）
const tusApi = createSimplifiedTusPreloadApi();

// 创建简化的下载 API（整合了 API 调用和事件监听器）
const downloadApi = createSimplifiedDownloadPreloadApi();

// 创建简化的压缩 API（整合了 API 调用和事件监听器）
const archiveApi = createSimplifiedArchivePreloadApi();

// 创建简化的解压缩 API（整合了 API 调用和事件监听器）
const extractionApi = createSimplifiedExtractionPreloadApi();

// 渲染进程的自定义 API
const api = {
  // 应用信息
  getAppVersion: () => ipcRenderer.invoke("get-app-version"),
  getPlatform: () => ipcRenderer.invoke("get-platform"),

  // 对话框 API
  showOpenDialog: (options: Electron.OpenDialogOptions) => ipcRenderer.invoke("show-open-dialog", options),
  showSaveDialog: (options: Electron.SaveDialogOptions) => ipcRenderer.invoke("show-save-dialog", options),
  showMessageBox: (options: Electron.MessageBoxOptions) => ipcRenderer.invoke("show-message-box", options),

  // 文件系统 API
  getFileInfo: (filePath: string) => ipcRenderer.invoke("get-file-info", filePath),

  // 拖拽文件处理 API
  dragDrop: {
    handleFileDrop: (filePaths: string[]) => ipcRenderer.invoke("drag-drop-handle-files", filePaths),
  },

  // 文件夹选择 API
  folderSelect: {
    selectFolderAndGetFiles: () => ipcRenderer.invoke("folder-select-and-get-files"),
  },

  // TUS 上传 API（整合了 API 调用和事件监听器）
  tus: tusApi,

  // StreamSaver 下载 API（整合了 API 调用和事件监听器）
  download: downloadApi,

  // Archive 压缩 API（整合了 API 调用和事件监听器）
  archive: archiveApi,

  // 7z 解压缩 API（整合了 API 调用和事件监听器）
  extraction: extractionApi,

  // 自动更新 API
  autoUpdater: {
    // 检查更新
    checkForUpdates: () => ipcRenderer.invoke("auto-updater:check-for-updates"),
    
    // 下载更新
    downloadUpdate: (versionInfo: VersionInfo) => ipcRenderer.invoke("auto-updater:download-update", versionInfo),
    
    // 安装更新
    installUpdate: () => ipcRenderer.invoke("auto-updater:install-update"),
    
    // 获取更新状态
    getStatus: () => ipcRenderer.invoke("auto-updater:get-status"),
    
    // 设置更新服务器地址
    setUpdateUrl: (url: string) => ipcRenderer.invoke("auto-updater:set-update-url", url),
    
    // 清理下载文件
    cleanupDownloads: () => ipcRenderer.invoke("auto-updater:cleanup-downloads"),
    
    // 事件监听器
    onUpdateCheckStart: (callback: () => void) => {
      ipcRenderer.on("auto-updater:update-check-start", () => callback());
    },
    
    onUpdateCheckResult: (callback: (result: any) => void) => {
      ipcRenderer.on("auto-updater:update-check-result", (_event, result) => callback(result));
    },
    
    onUpdateAvailable: (callback: (versionInfo: any) => void) => {
      ipcRenderer.on("auto-updater:update-available", (_event, versionInfo) => callback(versionInfo));
    },
    
    onUpdateNotAvailable: (callback: () => void) => {
      ipcRenderer.on("auto-updater:update-not-available", () => callback());
    },
    
    onDownloadStart: (callback: (versionInfo: any) => void) => {
      ipcRenderer.on("auto-updater:download-start", (_event, versionInfo) => callback(versionInfo));
    },
    
    onDownloadProgress: (callback: (progress: any) => void) => {
      ipcRenderer.on("auto-updater:download-progress", (_event, progress) => callback(progress));
    },
    
    onDownloadComplete: (callback: (filePath: string) => void) => {
      ipcRenderer.on("auto-updater:download-complete", (_event, filePath) => callback(filePath));
    },
    
    onDownloadError: (callback: (error: string) => void) => {
      ipcRenderer.on("auto-updater:download-error", (_event, error) => callback(error));
    },
    
    onInstallStart: (callback: () => void) => {
      ipcRenderer.on("auto-updater:install-start", () => callback());
    },
    
    onInstallComplete: (callback: () => void) => {
      ipcRenderer.on("auto-updater:install-complete", () => callback());
    },
    
    onInstallError: (callback: (error: string) => void) => {
      ipcRenderer.on("auto-updater:install-error", (_event, error) => callback(error));
    },
    
    onInstallCancelled: (callback: () => void) => {
      ipcRenderer.on("auto-updater:install-cancelled", () => callback());
    },
    
    onShowUpdateNotification: (callback: (versionInfo: any) => void) => {
      ipcRenderer.on("auto-updater:show-update-notification", (_event, versionInfo) => callback(versionInfo));
    },
    
    // 清理事件监听器
    removeAllListeners: () => {
      ipcRenderer.removeAllListeners("auto-updater:update-check-start");
      ipcRenderer.removeAllListeners("auto-updater:update-check-result");
      ipcRenderer.removeAllListeners("auto-updater:update-available");
      ipcRenderer.removeAllListeners("auto-updater:update-not-available");
      ipcRenderer.removeAllListeners("auto-updater:download-start");
      ipcRenderer.removeAllListeners("auto-updater:download-progress");
      ipcRenderer.removeAllListeners("auto-updater:download-complete");
      ipcRenderer.removeAllListeners("auto-updater:download-error");
      ipcRenderer.removeAllListeners("auto-updater:install-start");
      ipcRenderer.removeAllListeners("auto-updater:install-complete");
      ipcRenderer.removeAllListeners("auto-updater:install-error");
      ipcRenderer.removeAllListeners("auto-updater:install-cancelled");
      ipcRenderer.removeAllListeners("auto-updater:show-update-notification");
    }
  },

  // 主进程消息监听器
  onMainProcessMessage: (callback: (data: string) => void) => {
    ipcRenderer.on("main-process-message", (_event, data) => callback(data));
  },

  // 模块就绪通知
  onModulesReady: (callback: () => void) => {
    ipcRenderer.on("modules-ready", () => callback());
  },

  // 认证相关 API
  onAuthToken: (callback: (token: string) => void) => {
    ipcRenderer.on("auth-token", (_event, token) => callback(token));
  },

  clearAuthState: () => {
    ipcRenderer.send("clear-auth-state");
  },
};

// 使用 `contextBridge` API 向渲染进程暴露 Electron API
// 仅在启用上下文隔离时有效，否则直接添加到 DOM 全局对象
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld("electronAPI", api);
  } catch (error) {
    console.error(error);
  }
} else {
  // @ts-ignore (在 dts 文件中定义)
  window.electronAPI = api;
}
